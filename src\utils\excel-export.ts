import XLSX from 'xlsx-js-style';
import { formatCurrency } from './currency';

interface ExcelExportData {
  [key: string]: unknown;
}

interface ExcelExportOptions {
  filename: string;
  sheetName: string;
  data: ExcelExportData[];
  columns: {
    header: string;
    key: string;
    width?: number;
    style?: XLSX.CellStyle;
    format?: (value: unknown) => unknown;
  }[];
}

export const exportToExcel = (options: ExcelExportOptions): void => {
  const { filename, sheetName, data, columns } = options;

  // تحويل البيانات إلى تنسيق مناسب للجدول
  const excelData = data.map(row => {
    const newRow: Record<string, unknown> = {};
    columns.forEach(col => {
      // استخراج القيمة من الكائن بدعم للحقول المتداخلة مثل customer.name
      let value: ExcelExportData | unknown = row;
      const keys = col.key.split('.');
      for (const key of keys) {
        value = value?.[key];
      }
      
      // تطبيق التنسيق إذا تم توفيره
      if (col.format && value !== undefined && value !== null) {
        value = col.format(value);
      }
      
      newRow[col.header] = value;
    });
    return newRow;
  });

  // إنشاء ورقة عمل
  const worksheet = XLSX.utils.json_to_sheet(excelData);

  // تعيين عرض الأعمدة
  const colWidths: Record<string, { width: number }> = {};
  columns.forEach((col, index) => {
    if (col.width) {
      colWidths[XLSX.utils.encode_col(index)] = { width: col.width };
    }
  });
  worksheet['!cols'] = Object.values(colWidths);

  // إنشاء كتاب عمل
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

  // تصدير الملف
  XLSX.writeFile(workbook, `${filename}.xlsx`);
};

interface InvoiceItem {
  id?: number | string;
  product_name?: string;
  quantity?: number;
  price?: number;
  total?: number;
}

interface Invoice {
  invoice_number: string;
  customers: { name?: string };
  invoice_date: string;
  due_date?: string;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  invoice_items?: InvoiceItem[];
  payment_status: 'paid' | 'partial' | 'unpaid' | string;
  [key: string]: unknown;
}

export const exportInvoicesToExcel = (invoices: Invoice[]): void => {
  // تعريف الأعمدة
  const columns = [
    { header: 'رقم الفاتورة', key: 'invoice_number', width: 15 },
    { 
      header: 'العميل', 
      key: 'customers.name',
      width: 20,
      format: (value: string) => value || 'غير محدد'
    },
    { 
      header: 'تاريخ الإصدار', 
      key: 'invoice_date',
      width: 15,
      format: (value: string) => new Date(value).toLocaleDateString()
    },
    { 
      header: 'تاريخ الاستحقاق', 
      key: 'due_date',
      width: 15,
      format: (value: string) => value ? new Date(value).toLocaleDateString() : 'غير محدد'
    },
    { 
      header: 'المبلغ الإجمالي', 
      key: 'total_amount',
      width: 15,
      format: (value: number) => formatCurrency(value)
    },
    { 
      header: 'المبلغ المدفوع', 
      key: 'paid_amount',
      width: 15,
      format: (value: number) => formatCurrency(value)
    },
    { 
      header: 'المبلغ المتبقي', 
      key: 'remaining_amount',
      width: 15,
      format: (value: number) => formatCurrency(value)
    },
    { 
      header: 'عدد الأصناف', 
      key: 'invoice_items',
      width: 10,
      format: (value: InvoiceItem[] | undefined) => value?.length || 0
    },
    { 
      header: 'حالة الدفع', 
      key: 'payment_status',
      width: 15,
      format: (value: string) => {
        switch (value) {
          case 'paid': return 'مدفوع';
          case 'partial': return 'مدفوع جزئياً';
          case 'unpaid': return 'غير مدفوع';
          default: return value;
        }
      }
    },
  ];

  // تصدير الفواتير
  exportToExcel({
    filename: `فواتير_${new Date().toISOString().slice(0, 10)}`,
    sheetName: 'الفواتير',
    data: invoices,
    columns,
  });
};
