
import React from 'react';
import { StatsCard } from './StatsCard';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  TrendingUp, 
  Package, 
  Users, 
  ShoppingCart,
  AlertTriangle,
  Calendar,
  DollarSign
} from 'lucide-react';

export function Dashboard() {
  // بيانات وهمية للعرض
  const stats = [
    {
      title: 'إجمالي المبيعات اليوم',
      value: '45,280 دج',
      subtitle: '23 فاتورة',
      icon: TrendingUp,
      color: 'green' as const,
      trend: { value: '+12.5%', isPositive: true }
    },
    {
      title: 'المنتجات في المخزن',
      value: '1,247',
      subtitle: 'منتج متوفر',
      icon: Package,
      color: 'blue' as const,
      trend: { value: '-3.2%', isPositive: false }
    },
    {
      title: 'العملاء النشطين',
      value: '189',
      subtitle: 'عميل',
      icon: Users,
      color: 'orange' as const,
      trend: { value: '+8.1%', isPositive: true }
    },
    {
      title: 'فواتير اليوم',
      value: '23',
      subtitle: 'فاتورة',
      icon: ShoppingCart,
      color: 'red' as const,
      trend: { value: '+5.4%', isPositive: true }
    }
  ];

  const lowStockItems = [
    { name: 'شامبو هيد آند شولدرز 400مل', stock: 5, minStock: 20 },
    { name: 'معجون أسنان سيجنال 125مل', stock: 8, minStock: 25 },
    { name: 'صابون دوف الأبيض 90غ', stock: 12, minStock: 30 },
  ];

  const recentSales = [
    { customer: 'محمد أحمد', amount: '2,450 دج', time: '10:30 ص' },
    { customer: 'فاطمة علي', amount: '1,890 دج', time: '10:15 ص' },
    { customer: 'خالد محمود', amount: '3,200 دج', time: '09:45 ص' },
    { customer: 'عائشة حسن', amount: '980 دج', time: '09:30 ص' },
  ];

  return (
    <div className="space-y-6">
      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* المنتجات منخفضة المخزون */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-lg font-semibold">تنبيهات المخزون</CardTitle>
            <AlertTriangle className="w-5 h-5 text-orange-500" />
          </CardHeader>
          <CardContent className="space-y-4">
            {lowStockItems.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm text-gray-900">{item.name}</p>
                  <p className="text-xs text-gray-500">
                    المتوفر: {item.stock} | الحد الأدنى: {item.minStock}
                  </p>
                </div>
                <div className="text-left">
                  <span className="text-orange-600 font-bold text-sm">{item.stock}</span>
                </div>
              </div>
            ))}
            <Button size="sm" className="w-full mt-4">
              عرض جميع التنبيهات
            </Button>
          </CardContent>
        </Card>

        {/* المبيعات الأخيرة */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-lg font-semibold">المبيعات الأخيرة</CardTitle>
            <ShoppingCart className="w-5 h-5 text-green-500" />
          </CardHeader>
          <CardContent className="space-y-4">
            {recentSales.map((sale, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm text-gray-900">{sale.customer}</p>
                  <p className="text-xs text-gray-500">{sale.time}</p>
                </div>
                <div className="text-left">
                  <span className="text-green-600 font-bold text-sm">{sale.amount}</span>
                </div>
              </div>
            ))}
            <Button size="sm" className="w-full mt-4">
              عرض جميع المبيعات
            </Button>
          </CardContent>
        </Card>

        {/* إجراءات سريعة */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">إجراءات سريعة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" size="sm">
              <ShoppingCart className="ml-2 w-4 h-4" />
              فتح نقطة البيع
            </Button>
            <Button className="w-full justify-start" variant="outline" size="sm">
              <Package className="ml-2 w-4 h-4" />
              إضافة منتج جديد
            </Button>
            <Button className="w-full justify-start" variant="outline" size="sm">
              <Users className="ml-2 w-4 h-4" />
              إضافة عميل جديد
            </Button>
            <Button className="w-full justify-start" variant="outline" size="sm">
              <DollarSign className="ml-2 w-4 h-4" />
              عرض التقارير
            </Button>
            <Button className="w-full justify-start" variant="outline" size="sm">
              <Calendar className="ml-2 w-4 h-4" />
              جرد المخزون
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* الرسم البياني للمبيعات */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">مبيعات الأسبوع الحالي</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">سيتم إضافة الرسوم البيانية في التحديث القادم</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
