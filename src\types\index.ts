
export interface Product {
  id: string;
  name: string;
  nameAr: string;
  category: string;
  brand: string;
  barcode: string;
  unit: string;
  purchasePrice: number;
  salePrice: number;
  stock: number;
  minStock: number;
  description?: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  city: string;
  address?: string;
  balance: number;
  totalPurchases: number;
  createdAt: Date;
}

export interface Supplier {
  id: string;
  name: string;
  phone: string;
  address: string;
  balance: number;
  totalPurchases: number;
  createdAt: Date;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  type: 'sale' | 'purchase';
  customerId?: string;
  supplierId?: string;
  items: InvoiceItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paymentMethod: 'cash' | 'card' | 'credit';
  status: 'paid' | 'pending' | 'cancelled';
  createdAt: Date;
}

export interface InvoiceItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface User {
  id: string;
  username: string;
  fullName: string;
  role: 'admin' | 'cashier' | 'accountant' | 'warehouse';
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
}
