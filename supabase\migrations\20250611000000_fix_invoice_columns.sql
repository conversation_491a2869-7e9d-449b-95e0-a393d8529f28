-- Add missing columns to invoices table
ALTER TABLE IF EXISTS invoices 
  ADD COLUMN IF NOT EXISTS discount_percent DECIMAL(5,2) DEFAULT 0,
  ADD COLUMN IF NOT EXISTS tax_rate DECIMAL(5,2) DEFAULT 0;

-- Refresh PostgREST schema cache
SELECT pg_notify('pgrst', 'reload schema');

-- Verify columns exist and data types
DO $$ 
BEGIN
  -- Check if columns exist and have the right data type
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'invoices' AND column_name = 'discount_percent'
  ) THEN
    RAISE NOTICE 'Column discount_percent does not exist in invoices table';
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'invoices' AND column_name = 'tax_rate'
  ) THEN
    RAISE NOTICE 'Column tax_rate does not exist in invoices table';
  END IF;
END $$;
