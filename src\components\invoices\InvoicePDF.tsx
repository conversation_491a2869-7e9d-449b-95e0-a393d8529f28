import React from 'react';
import { 
  Document, 
  Page, 
  Text, 
  View, 
  StyleSheet, 
  PDFDownloadLink as ReactPDFDownloadLink
} from '@react-pdf/renderer';
import { InvoiceType, PaymentMethod, InvoiceStatus, PaymentStatus } from '@/types/invoice';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

// Create styles
const styles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 11,
    paddingTop: 30,
    paddingBottom: 30,
    paddingHorizontal: 35,
    direction: 'rtl',
  },
  logo: {
    width: 74,
    height: 66,
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  title: {
    fontSize: 20,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 20,
  },
  header: {
    fontSize: 12,
    marginBottom: 20,
    textAlign: 'center',
    color: 'grey',
  },
  invoiceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  invoiceInfoColumn: {
    width: '48%',
  },
  infoGroup: {
    marginBottom: 10,
  },
  infoLabel: {
    fontWeight: 'bold',
    marginBottom: 3,
  },
  infoValue: {
    marginBottom: 2,
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bfbfbf',
    marginBottom: 20,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomColor: '#bfbfbf',
    borderBottomWidth: 1,
  },
  tableRowHeader: {
    backgroundColor: '#f0f0f0',
    fontWeight: 'bold',
  },
  tableCol: {
    padding: 5,
    borderRightColor: '#bfbfbf',
    borderRightWidth: 1,
    textAlign: 'right',
  },
  tableColLeft: {
    textAlign: 'left',
  },
  tableColCenter: {
    textAlign: 'center',
  },
  tableColLast: {
    borderRightWidth: 0,
  },
  tableColHeader: {
    fontWeight: 'bold',
    padding: 5,
  },
  tableColHeaderCenter: {
    fontWeight: 'bold',
    padding: 5,
    textAlign: 'center',
  },
  tableColHeaderRight: {
    fontWeight: 'bold',
    padding: 5,
    textAlign: 'right',
  },
  colNum: {
    width: '5%',
  },
  colName: {
    width: '40%',
  },
  colPrice: {
    width: '15%',
  },
  colQty: {
    width: '10%',
  },
  colDiscount: {
    width: '10%',
  },
  colTotal: {
    width: '20%',
  },
  summary: {
    marginTop: 20,
    marginLeft: 'auto',
    width: '35%',
  },
  summaryRow: {
    flexDirection: 'row',
    borderBottomColor: '#bfbfbf',
    borderBottomWidth: 1,
    padding: 5,
  },
  summaryKey: {
    width: '60%',
    textAlign: 'left',
    fontWeight: 'bold',
  },
  summaryValue: {
    width: '40%',
    textAlign: 'right',
  },
  notes: {
    marginTop: 20,
    padding: 10,
    borderColor: '#bfbfbf',
    borderWidth: 1,
    borderRadius: 5,
  },
  notesTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    textAlign: 'center',
    fontSize: 10,
    color: 'grey',
  },
  signature: {
    marginTop: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  signatureBox: {
    width: '45%',
    borderTopWidth: 1,
    borderTopColor: '#bfbfbf',
    paddingTop: 5,
    textAlign: 'center',
  },
  statusBadge: {
    padding: 4,
    borderRadius: 4,
    fontSize: 8,
    textAlign: 'center',
    width: 60,
    marginBottom: 5,
    marginLeft: 'auto',
  },
  statusCompleted: {
    backgroundColor: '#10B981',
    color: '#FFFFFF',
  },
  statusPaid: {
    backgroundColor: '#10B981',
    color: '#FFFFFF',
  },
  statusPartial: {
    backgroundColor: '#F59E0B',
    color: '#FFFFFF',
  },
  statusUnpaid: {
    backgroundColor: '#EF4444',
    color: '#FFFFFF',
  },
  statusDraft: {
    backgroundColor: '#E5E7EB',
    color: '#4B5563',
  },
  statusCanceled: {
    backgroundColor: '#EF4444',
    color: '#FFFFFF',
  },
  statusReturned: {
    backgroundColor: '#8B5CF6',
    color: '#FFFFFF',
  },
});

// Format currency function
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-DZ', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Invoice status and payment status badges
const InvoiceStatusBadge = ({ status }: { status: InvoiceStatus }) => {
  switch (status) {
    case InvoiceStatus.COMPLETED:
      return <Text style={[styles.statusBadge, styles.statusCompleted]}>مكتملة</Text>;
    case InvoiceStatus.DRAFT:
      return <Text style={[styles.statusBadge, styles.statusDraft]}>مسودة</Text>;
    case InvoiceStatus.CANCELED:
      return <Text style={[styles.statusBadge, styles.statusCanceled]}>ملغية</Text>;
    case InvoiceStatus.RETURNED:
      return <Text style={[styles.statusBadge, styles.statusReturned]}>مرتجعة</Text>;
    default:
      return <Text style={[styles.statusBadge, styles.statusDraft]}>{status}</Text>;
  }
};

const PaymentStatusBadge = ({ status }: { status: PaymentStatus }) => {
  switch (status) {
    case PaymentStatus.PAID:
      return <Text style={[styles.statusBadge, styles.statusPaid]}>مدفوعة</Text>;
    case PaymentStatus.PARTIAL:
      return <Text style={[styles.statusBadge, styles.statusPartial]}>مدفوعة جزئيًا</Text>;
    case PaymentStatus.UNPAID:
      return <Text style={[styles.statusBadge, styles.statusUnpaid]}>غير مدفوعة</Text>;
    default:
      return <Text style={[styles.statusBadge, styles.statusUnpaid]}>{status}</Text>;
  }
};

const getPaymentMethodText = (method: PaymentMethod) => {
  switch (method) {
    case PaymentMethod.CASH:
      return "نقداً";
    case PaymentMethod.BANK_CARD:
      return "بطاقة بنكية";
    case PaymentMethod.CREDIT:
      return "آجل";
    case PaymentMethod.MIXED:
      return "مختلط";
    default:
      return method;
  }
};

// PDF Document Component
interface InvoiceItem {
  name: string;
  barcode?: string;
  unit_price: number;
  quantity: number;
  discount_amount?: number;
  total_price: number;
}

interface Invoice {
  invoice_number: string;
  customer_name?: string;
  supplier_name?: string;
  original_invoice_number?: string;
  return_reason?: string;
  created_at: string;
  payment_method: PaymentMethod;
  status: InvoiceStatus;
  payment_status: PaymentStatus;
  subtotal: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  notes?: string;
}

export const PDFDocument = ({ invoice, items, invoiceType }: { invoice: Invoice, items: InvoiceItem[], invoiceType: InvoiceType }) => {
  const isSalesInvoice = invoiceType === InvoiceType.SALES || invoiceType === InvoiceType.SALES_RETURN;
  const isReturnInvoice = invoiceType === InvoiceType.SALES_RETURN || invoiceType === InvoiceType.PURCHASE_RETURN;
  
  let invoiceTitle = "";
  switch (invoiceType) {
    case InvoiceType.SALES:
      invoiceTitle = "فاتورة مبيعات";
      break;
    case InvoiceType.PURCHASE:
      invoiceTitle = "فاتورة مشتريات";
      break;
    case InvoiceType.SALES_RETURN:
      invoiceTitle = "فاتورة إرجاع مبيعات";
      break;
    case InvoiceType.PURCHASE_RETURN:
      invoiceTitle = "فاتورة إرجاع مشتريات";
      break;
  }
  
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <Text style={styles.title}>متجر الإلكترونيات</Text>
        <Text style={styles.header}>
          {invoiceTitle} | {invoice.invoice_number}
        </Text>
        
        <View style={styles.invoiceInfo}>
          <View style={styles.invoiceInfoColumn}>
            <View style={styles.infoGroup}>
              <Text style={styles.infoLabel}>
                {isSalesInvoice ? 'بيانات العميل:' : 'بيانات المورد:'}
              </Text>
              <Text style={styles.infoValue}>
                {isSalesInvoice ? invoice.customer_name : invoice.supplier_name || 'غير محدد'}
              </Text>
            </View>
            
            {isReturnInvoice && invoice.original_invoice_number && (
              <View style={styles.infoGroup}>
                <Text style={styles.infoLabel}>الفاتورة الأصلية:</Text>
                <Text style={styles.infoValue}>{invoice.original_invoice_number}</Text>
              </View>
            )}
            
            {isReturnInvoice && invoice.return_reason && (
              <View style={styles.infoGroup}>
                <Text style={styles.infoLabel}>سبب الإرجاع:</Text>
                <Text style={styles.infoValue}>{invoice.return_reason}</Text>
              </View>
            )}
          </View>
          
          <View style={styles.invoiceInfoColumn}>
            <View style={styles.infoGroup}>
              <Text style={styles.infoLabel}>رقم الفاتورة:</Text>
              <Text style={styles.infoValue}>{invoice.invoice_number}</Text>
            </View>
            
            <View style={styles.infoGroup}>
              <Text style={styles.infoLabel}>التاريخ:</Text>
              <Text style={styles.infoValue}>
                {format(new Date(invoice.created_at), 'dd MMMM yyyy', { locale: ar })}
              </Text>
            </View>
            
            <View style={styles.infoGroup}>
              <Text style={styles.infoLabel}>طريقة الدفع:</Text>
              <Text style={styles.infoValue}>
                {getPaymentMethodText(invoice.payment_method)}
              </Text>
            </View>
            
            <View>
              <InvoiceStatusBadge status={invoice.status} />
              <PaymentStatusBadge status={invoice.payment_status} />
            </View>
          </View>
        </View>
        
        <View style={styles.table}>
          {/* Table Header */}
          <View style={[styles.tableRow, styles.tableRowHeader]}>
            <View style={[styles.tableCol, styles.colNum]}>
              <Text style={styles.tableColHeaderCenter}>#</Text>
            </View>
            <View style={[styles.tableCol, styles.colName]}>
              <Text style={styles.tableColHeader}>المنتج</Text>
            </View>
            <View style={[styles.tableCol, styles.colPrice]}>
              <Text style={styles.tableColHeaderCenter}>السعر</Text>
            </View>
            <View style={[styles.tableCol, styles.colQty]}>
              <Text style={styles.tableColHeaderCenter}>الكمية</Text>
            </View>
            <View style={[styles.tableCol, styles.colDiscount]}>
              <Text style={styles.tableColHeaderCenter}>الخصم</Text>
            </View>
            <View style={[styles.tableCol, styles.colTotal, styles.tableColLast]}>
              <Text style={styles.tableColHeaderRight}>الإجمالي</Text>
            </View>
          </View>
          
          {/* Table Body */}
          {items.map((item, index) => (
            <View key={index} style={styles.tableRow}>
              <View style={[styles.tableCol, styles.colNum, styles.tableColCenter]}>
                <Text>{index + 1}</Text>
              </View>
              <View style={[styles.tableCol, styles.colName]}>
                <Text>{item.name}</Text>
                {item.barcode && <Text style={{ fontSize: 8 }}>{item.barcode}</Text>}
              </View>
              <View style={[styles.tableCol, styles.colPrice, styles.tableColCenter]}>
                <Text>{formatCurrency(item.unit_price)} دج</Text>
              </View>
              <View style={[styles.tableCol, styles.colQty, styles.tableColCenter]}>
                <Text>{item.quantity}</Text>
              </View>
              <View style={[styles.tableCol, styles.colDiscount, styles.tableColCenter]}>
                <Text>{item.discount_amount ? formatCurrency(item.discount_amount) : '-'}</Text>
              </View>
              <View style={[styles.tableCol, styles.colTotal, styles.tableColLast, styles.tableColHeaderRight]}>
                <Text>{formatCurrency(item.total_price)} دج</Text>
              </View>
            </View>
          ))}
        </View>
        
        <View style={styles.summary}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryKey}>المجموع الفرعي:</Text>
            <Text style={styles.summaryValue}>{formatCurrency(invoice.subtotal)} دج</Text>
          </View>
          
          {invoice.discount_amount > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryKey}>الخصم:</Text>
              <Text style={styles.summaryValue}>{formatCurrency(invoice.discount_amount)} دج</Text>
            </View>
          )}
          
          {invoice.tax_amount > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryKey}>الضريبة:</Text>
              <Text style={styles.summaryValue}>{formatCurrency(invoice.tax_amount)} دج</Text>
            </View>
          )}
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryKey}>الإجمالي:</Text>
            <Text style={styles.summaryValue}>{formatCurrency(invoice.total_amount)} دج</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryKey}>المدفوع:</Text>
            <Text style={styles.summaryValue}>{formatCurrency(invoice.paid_amount)} دج</Text>
          </View>
          
          {invoice.payment_status !== PaymentStatus.PAID && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryKey}>المتبقي:</Text>
              <Text style={styles.summaryValue}>{formatCurrency(invoice.remaining_amount)} دج</Text>
            </View>
          )}
        </View>
        
        {invoice.notes && (
          <View style={styles.notes}>
            <Text style={styles.notesTitle}>ملاحظات:</Text>
            <Text>{invoice.notes}</Text>
          </View>
        )}
        
        <View style={styles.signature}>
          <View style={styles.signatureBox}>
            <Text>توقيع المستلم</Text>
          </View>
          <View style={styles.signatureBox}>
            <Text>توقيع المسؤول</Text>
          </View>
        </View>
        
        <Text style={styles.footer}>
          {format(new Date(invoice.created_at), 'dd/MM/yyyy HH:mm')}
        </Text>
      </Page>
    </Document>
  );
};

// Export the PDFDownloadLink component
export const PDFDownloadLink = ReactPDFDownloadLink;
