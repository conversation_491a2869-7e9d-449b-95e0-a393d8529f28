import * as React from "react";
import { format, Locale } from "date-fns";
import { ar } from "date-fns/locale";
import { Calendar as CalendarIcon } from "lucide-react";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DateRangePickerBaseProps {
  value?: DateRange | undefined;
  onChange?: (date: DateRange | undefined) => void;
  placeholder?: string;
  locale?: Locale;
  align?: "center" | "start" | "end";
}

type DateRangePickerProps = DateRangePickerBaseProps & Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'>;

export function DateRangePicker({
  value,
  onChange,
  className,
  placeholder = "Select date range",
  locale = ar,
  align = "center",
}: DateRangePickerProps) {
  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "min-w-[300px] justify-start text-right font-normal",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="ml-2 h-4 w-4" />
            {value?.from ? (
              value.to ? (
                <>
                  {format(value.from, "dd/MM/yyyy", { locale })} -{" "}
                  {format(value.to, "dd/MM/yyyy", { locale })}
                </>
              ) : (
                format(value.from, "dd/MM/yyyy", { locale })
              )
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align={align}>
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={value?.from}
            selected={value}
            onSelect={onChange}
            numberOfMonths={2}
            locale={locale}
            dir="rtl"
            className="px-2"
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
