import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import {
    Bar<PERSON>hart,
    // CreditCard,
    // Package,
    // User,
} from "lucide-react"

interface StatsCardProps {
    title: string;
    value: string;
    color: 'purple' | 'red' | 'blue' | 'green';
    // icon: React.ReactNode; // Remove icon from props
}

function StatsCard({ title, value, color }: StatsCardProps) {
    let bgColor = 'bg-purple-500';
    switch (color) {
        case 'red':
            bgColor = 'bg-red-500';
            break;
        case 'blue':
            bgColor = 'bg-blue-500';
            break;
        case 'green':
            bgColor = 'bg-green-500';
            break;
    }

    return (
        <Card className="shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{title}</CardTitle>
                {/* 
                <div>
                    {icon}
                </div>
                 */}
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value}</div>
            </CardContent>
        </Card>
    );
}

const SimpleDashboard = () => {
    return (
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard title="إجمالي العملاء" value="270" color="purple" />
            <StatsCard title="إجمالي الموردين" value="38" color="red" />
            <StatsCard title="عدد المنتجات" value="501" color="blue" />
            <StatsCard title="المبيعات اليوم" value="2,500 ر.س" color="green" />

            <Card className="col-span-4">
                <CardHeader>
                    <CardTitle>نظرة عامة على المبيعات</CardTitle>
                </CardHeader>
                <CardContent>
                    <BarChart className="h-4 w-4 mr-2" />
                    {/* Sales overview chart will go here */}
                    <p className="text-sm text-muted-foreground">
                        {/* Placeholder for sales overview content */}
                        مخطط بياني للمبيعات سيظهر هنا
                    </p>
                </CardContent>
            </Card>
        </div>
    );
};

export default SimpleDashboard;
