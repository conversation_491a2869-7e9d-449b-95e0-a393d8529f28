
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: LucideIcon;
  color: 'blue' | 'green' | 'orange' | 'red';
  trend?: {
    value: string;
    isPositive: boolean;
  };
}

const colorVariants = {
  blue: 'from-blue-500 to-blue-600',
  green: 'from-green-500 to-green-600',
  orange: 'from-orange-500 to-orange-600',
  red: 'from-red-500 to-red-600',
};

export function StatsCard({ title, value, subtitle, icon: Icon, color, trend }: StatsCardProps) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <CardContent className="p-0">
        <div className={`bg-gradient-to-r ${colorVariants[color]} p-6 text-white`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/80 text-sm font-medium">{title}</p>
              <p className="text-2xl font-bold mt-1">{value}</p>
              <p className="text-white/70 text-xs mt-1">{subtitle}</p>
            </div>
            <div className="bg-white/20 p-3 rounded-lg">
              <Icon className="w-6 h-6" />
            </div>
          </div>
        </div>
        
        {trend && (
          <div className="p-4 bg-gray-50">
            <div className="flex items-center gap-2">
              <span className={`text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {trend.isPositive ? '↗' : '↘'} {trend.value}
              </span>
              <span className="text-gray-500 text-sm">مقارنة بالشهر الماضي</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
