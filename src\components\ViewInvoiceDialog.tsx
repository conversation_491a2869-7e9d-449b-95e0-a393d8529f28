import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Des<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Calendar, Download, FileText, Printer, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { formatCurrency } from "@/utils/currency";

interface ViewInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: string | null;
}

interface Invoice {
  id: string;
  invoice_number: string;
  customer_id: string;
  invoice_date: string;
  due_date: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string | null;
  notes: string | null;
  type: string;
  created_at: string;
  customers?: {
    name: string;
    phone: string;
    email: string | null;
  };
  invoice_items?: InvoiceItem[];
}

interface InvoiceItem {
  id: string;
  invoice_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  products?: {
    name: string;
    code?: string;
  };
}

export function ViewInvoiceDialog({ open, onOpenChange, invoiceId }: ViewInvoiceDialogProps) {
  const { toast } = useToast();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // جلب بيانات الفاتورة عند فتح الحوار
  useEffect(() => {
    const fetchInvoiceData = async () => {
      if (!open || !invoiceId) return;
      
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('invoices')
          .select(`
            *,
            customers (name, phone, email),
            invoice_items (
              *,
              products (name, code)
            )
          `)
          .eq('id', invoiceId)
          .single();

        if (error) throw error;
        setInvoice(data as unknown as Invoice);
      } catch (error) {
        console.error('Error fetching invoice:', error);
        toast({
          title: "خطأ",
          description: "حدث خطأ في جلب بيانات الفاتورة",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceData();
  }, [open, invoiceId, toast]);

  const handlePrint = () => {
    // فتح نافذة طباعة جديدة مع محتويات الفاتورة
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = `
        <html dir="rtl">
        <head>
          <title>فاتورة ${invoice?.invoice_number}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .header { display: flex; justify-content: space-between; border-bottom: 1px solid #ccc; padding-bottom: 10px; }
            .invoice-info { margin: 20px 0; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            table th, table td { text-align: right; padding: 10px; border-bottom: 1px solid #eee; }
            .total-section { margin-top: 20px; text-align: left; }
            .logo { font-size: 24px; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">دي زد - إدارة المخزون</div>
            <div>
              <div>فاتورة ${invoice?.invoice_number}</div>
              <div>تاريخ: ${new Date(invoice?.invoice_date || '').toLocaleDateString('ar-SA')}</div>
            </div>
          </div>
          
          <div class="invoice-info">
            <div><strong>العميل:</strong> ${invoice?.customers?.name || 'غير محدد'}</div>
            <div><strong>رقم الهاتف:</strong> ${invoice?.customers?.phone || 'غير محدد'}</div>
            <div><strong>البريد الإلكتروني:</strong> ${invoice?.customers?.email || 'غير محدد'}</div>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>المنتج</th>
                <th>الكود</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>المجموع</th>
              </tr>
            </thead>
            <tbody>
              ${invoice?.invoice_items?.map(item => `
                <tr>
                  <td>${item.products?.name || 'غير معروف'}</td>
                  <td>${item.products?.code || 'غير معروف'}</td>
                  <td>${item.quantity}</td>
                  <td>${item.unit_price.toFixed(2)} ريال</td>
                  <td>${item.total_price.toFixed(2)} ريال</td>
                </tr>
              `).join('') || ''}
            </tbody>
          </table>
          
          <div class="total-section">
            <div><strong>المجموع:</strong> ${invoice?.total_amount.toFixed(2)} ريال</div>
            <div><strong>المدفوع:</strong> ${invoice?.paid_amount.toFixed(2)} ريال</div>
            <div><strong>المتبقي:</strong> ${invoice?.remaining_amount.toFixed(2)} ريال</div>
            <div><strong>طريقة الدفع:</strong> ${invoice?.payment_method === 'cash' ? 'نقداً' : invoice?.payment_method === 'card' ? 'بطاقة ائتمان' : invoice?.payment_method}</div>
            <div><strong>حالة الدفع:</strong> ${
              invoice?.payment_status === 'paid' ? 'مدفوع' :
              invoice?.payment_status === 'partial' ? 'مدفوع جزئياً' :
              'غير مدفوع'
            }</div>
          </div>
          
          ${invoice?.notes ? `<div style="margin-top: 20px;"><strong>ملاحظات:</strong> ${invoice.notes}</div>` : ''}
          
          <div style="margin-top: 40px; text-align: center;">
            <p>شكراً لك</p>
          </div>
        </body>
        </html>
      `;
      
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();
      // الانتظار للتأكد من تحميل المحتوى قبل الطباعة
      setTimeout(() => {
        printWindow.print();
      }, 500);
    }
  };
  const handleDownloadPDF = async () => {
    if (!invoice) return;
    
    try {
      const { downloadInvoicePDF } = await import("@/utils/pdf-generator");
      const result = await downloadInvoicePDF(invoice);
      
      if (result) {
        toast({
          title: "تم بنجاح",
          description: "تم تحميل الفاتورة بصيغة PDF بنجاح",
        });
      } else {
        throw new Error("فشل إنشاء ملف PDF");
      }
    } catch (error) {
      console.error("Error downloading invoice as PDF:", error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحميل ملف PDF",
        variant: "destructive",
      });
    }
  };

  // تحميل - شارع فارغة
  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تحميل...</DialogTitle>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  }

  // عرض فارغ إذا لم يتم تحميل الفاتورة بعد
  if (!invoice) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>عرض الفاتورة #{invoice.invoice_number}</DialogTitle>
          <DialogDescription>تفاصيل الفاتورة وعناصرها</DialogDescription>
        </DialogHeader>

        {/* رأس الفاتورة مع أزرار الإجراءات */}
        <div className="bg-muted/20 p-4 rounded-lg mb-4">
          <div className="flex justify-between mb-4">
            <div>
              <h3 className="font-bold text-xl">{invoice.invoice_number}</h3>
              <div className="text-sm text-muted-foreground">
                تم الإنشاء في {new Date(invoice.created_at).toLocaleDateString('ar-SA')}
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={handlePrint}>
                <Printer className="w-4 h-4 ml-2" />
                طباعة
              </Button>
              <Button size="sm" variant="outline" onClick={handleDownloadPDF}>
                <Download className="w-4 h-4 ml-2" />
                تحميل PDF
              </Button>
            </div>
          </div>

          {/* بيانات العميل والفاتورة */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <User className="w-4 h-4 ml-2 text-muted-foreground" />
                <span className="font-medium">العميل:</span>
              </div>
              <div className="font-semibold">{invoice.customers?.name || "غير محدد"}</div>
              {invoice.customers?.phone && <div className="text-sm">{invoice.customers.phone}</div>}
              {invoice.customers?.email && <div className="text-sm">{invoice.customers.email}</div>}
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <Calendar className="w-4 h-4 ml-2 text-muted-foreground" />
                <span className="font-medium">تاريخ الفاتورة:</span>
              </div>
              <div className="font-semibold">{new Date(invoice.invoice_date).toLocaleDateString('ar-SA')}</div>
              {invoice.due_date && (
                <div className="text-sm">
                  تاريخ الاستحقاق: {new Date(invoice.due_date).toLocaleDateString('ar-SA')}
                </div>
              )}
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <FileText className="w-4 h-4 ml-2 text-muted-foreground" />
                <span className="font-medium">معلومات الدفع:</span>
              </div>
              <div>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mr-2 bg-blue-50 text-blue-700">
                  {invoice.payment_method === 'cash' ? 'نقداً' : invoice.payment_method === 'card' ? 'بطاقة' : invoice.payment_method}
                </span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  invoice.payment_status === 'paid' ? 'bg-green-50 text-green-700' :
                  invoice.payment_status === 'partial' ? 'bg-yellow-50 text-yellow-700' :
                  'bg-red-50 text-red-700'
                }`}>
                  {invoice.payment_status === 'paid' ? 'مدفوع' :
                   invoice.payment_status === 'partial' ? 'مدفوع جزئياً' :
                   'غير مدفوع'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* جدول عناصر الفاتورة */}
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">المنتج</TableHead>
                <TableHead className="text-right">الكمية</TableHead>
                <TableHead className="text-right">سعر الوحدة</TableHead>
                <TableHead className="text-right">المجموع</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoice.invoice_items?.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.products?.name || 'غير معروف'}</TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{formatCurrency(item.unit_price)}</TableCell>
                  <TableCell>{formatCurrency(item.total_price)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* ملخص المبالغ */}
        <div className="mt-4 border-t pt-4">
          <div className="flex justify-end">
            <div className="w-64">
              <div className="flex justify-between py-2">
                <span className="text-muted-foreground">المجموع:</span>
                <span className="font-medium">{formatCurrency(invoice.total_amount)}</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-muted-foreground">المدفوع:</span>
                <span className="font-medium text-green-600">{formatCurrency(invoice.paid_amount)}</span>
              </div>
              <div className="flex justify-between py-2 border-t">
                <span className="font-medium">المتبقي:</span>
                <span className="font-bold text-red-600">{formatCurrency(invoice.remaining_amount)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* ملاحظات إذا وجدت */}
        {invoice.notes && (
          <div className="mt-4 pt-4 border-t">
            <h4 className="font-medium mb-2">ملاحظات:</h4>
            <div className="p-3 bg-muted/20 rounded-lg text-sm">{invoice.notes}</div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
