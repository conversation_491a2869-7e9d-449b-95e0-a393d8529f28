import { 
  Home, 
  ShoppingCart, 
  Package, 
  Users, 
  TrendingUp,
  Settings,
  Store,
  Truck,
  Menu,
  X,
  Receipt
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { UserMenu } from "./UserMenu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import "./top-navbar.css";

const menuItems = [
  {
    title: "لوحة التحكم",
    url: "/",
    icon: Home,
  },  {
    title: "نقطة البيع",
    url: "/pos",
    icon: ShoppingCart,
  },
  {
    title: "الفواتير",
    url: "/invoices",
    icon: Receipt,
  },
  {
    title: "إدارة المنتجات",
    url: "/products",
    icon: Package,
  },
  {
    title: "إدارة العملاء",
    url: "/customers",
    icon: Users,
  },
  {
    title: "إدارة الموردين",
    url: "/suppliers",
    icon: Truck,
  },
  {
    title: "التقارير",
    url: "/reports",
    icon: TrendingUp,
  },
  {
    title: "الإعدادات",
    url: "/settings",
    icon: Settings,
  },
];

export function TopNavbar() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeItem, setActiveItem] = useState(location.pathname);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setActiveItem(location.pathname);
  }, [location.pathname]);

  const handleNavigation = (url: string) => {
    setActiveItem(url);
    navigate(url);
    setIsOpen(false);
  };

  return (
    <nav className="top-navbar">
      <div className="navbar-brand">
        <div className="app-logo">
          <Store className="w-6 h-6 text-white" />
        </div>
        <h1 className="brand-text">نظام إدارة المتاجر</h1>
      </div>
      
      {/* Desktop Navigation */}
      <div className="navbar-links hidden md:flex">
        {menuItems.map((item) => (
          <button
            key={item.title}
            className={`navbar-item ${activeItem === item.url ? 'navbar-item-active' : ''}`}
            onClick={() => handleNavigation(item.url)}
          >
            <div className="navbar-icon">
              <item.icon className="w-5 h-5" />
            </div>
            <span>{item.title}</span>
          </button>
        ))}
      </div>

      <div className="flex-1"></div>
      
      {/* Mobile Menu Button */}
      <div className="block md:hidden">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="sm">
              <Menu className="w-5 h-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="p-0">
            <div className="p-4 flex flex-col">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-bold">القائمة</h2>
                <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                  <X className="w-5 h-5" />
                </Button>
              </div>
              <div className="flex flex-col space-y-1">
                {menuItems.map((item) => (
                  <button
                    key={item.title}
                    className={`mobile-navbar-item flex items-center p-3 rounded-md ${
                      activeItem === item.url ? 'bg-primary text-white' : 'text-gray-600'
                    }`}
                    onClick={() => handleNavigation(item.url)}
                  >
                    <item.icon className="w-5 h-5 ml-3" />
                    <span>{item.title}</span>
                  </button>
                ))}
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      <div className="user-section">
        <UserMenu />
      </div>
    </nav>
  );
}
