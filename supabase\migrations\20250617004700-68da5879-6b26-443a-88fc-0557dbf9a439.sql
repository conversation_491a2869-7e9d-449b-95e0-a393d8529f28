
-- Drop the existing check constraints
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_payment_status_check;
ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_payment_method_check;

-- Add correct check constraints that match the enum definitions
ALTER TABLE invoices ADD CONSTRAINT invoices_payment_status_check 
CHECK (payment_status IN ('paid', 'partial', 'unpaid'));

ALTER TABLE invoices ADD CONSTRAINT invoices_payment_method_check 
CHECK (payment_method IN ('cash', 'bank_card', 'credit', 'mixed') OR payment_method IS NULL);
