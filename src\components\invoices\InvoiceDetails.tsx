import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  ArrowLeft, 
  Printer, 
  FileDown, 
  Edit,
  RotateCcw,
  CheckCircle,
  XCircle,
  Clock,
  Receipt,
  CreditCard,
  DollarSign,
  ShoppingBag
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { 
  InvoiceType, 
  InvoiceStatus, 
  PaymentStatus, 
  PaymentMethod,
  InvoiceItem
} from "@/types/invoice";

interface PaymentTransaction {
  payment_date: string;
  payment_method: PaymentMethod;
  amount: number;
}

interface Invoice {
  id: string;
  invoice_number: string;
  reference_number?: string;
  created_at: string;
  status: InvoiceStatus;
  payment_status: PaymentStatus;
  payment_method: PaymentMethod;
  customer_name?: string;
  supplier_name?: string;
  warehouse_name?: string;
  subtotal: number;
  discount_amount: number;
  discount_percent: number;
  tax_amount: number;
  tax_percent: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  notes?: string;
  return_reason?: string;
  returned_from_invoice_id?: string;
  original_invoice_number?: string;
}
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { formatCurrency } from "@/utils/currency";
import { PDFDocument, PDFDownloadLink } from "@/components/invoices/InvoicePDF";

interface InvoiceDetailsProps {
  invoiceId: string;
  invoiceType: InvoiceType;
}

function InvoiceDetails({ invoiceId, invoiceType }: InvoiceDetailsProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [paymentHistory, setPaymentHistory] = useState<PaymentTransaction[]>([]);
  
  // Determine if this is a sales or purchase invoice
  const isSalesInvoice = invoiceType === InvoiceType.SALES || invoiceType === InvoiceType.SALES_RETURN;
  const isReturnInvoice = invoiceType === InvoiceType.SALES_RETURN || invoiceType === InvoiceType.PURCHASE_RETURN;
  
  useEffect(() => {
    fetchInvoiceDetails();
  }, [invoiceId, invoiceType]);
  const fetchInvoiceDetails = async () => {
    setIsLoading(true);
    try {
      // Use the common invoice tables for all invoice types
      const tableName = 'invoices';
      const itemsTableName = 'invoice_items';
      
      // Fetch invoice details
      const { data: invoiceData, error: invoiceError } = await supabase
        .from(tableName)
        .select('id, invoice_number, created_at, status, payment_status, payment_method, customer_name, supplier_name, warehouse_name, subtotal, discount_amount, discount_percent, tax_amount, tax_percent, total_amount, paid_amount, remaining_amount, notes, return_reason, returned_from_invoice_id, original_invoice_number')
        .eq('id', invoiceId)
        .single();
      
      if (invoiceError) throw invoiceError;
      
      // Fetch invoice items
      const { data: itemsData, error: itemsError } = await supabase
        .from(itemsTableName)
        .select('*')
        .eq('invoice_id', invoiceId);
      
      if (itemsError) throw itemsError;
      
      // Since payment_transactions table is not available in our Supabase schema,
      // we'll set an empty array for payment history for now
      // TODO: Implement proper payment history fetching when the table is available
      
      // Transform invoiceData to match the Invoice interface structure
      const transformedInvoice: Invoice = {
        id: invoiceData && 'id' in invoiceData ? invoiceData.id as string : '',
        invoice_number: invoiceData && 'invoice_number' in invoiceData ? invoiceData.invoice_number as string : '',
        reference_number: null, // Removed reference to non-existent property
        created_at: invoiceData && 'created_at' in invoiceData ? invoiceData.created_at as string : '',
        status: InvoiceStatus.DRAFT, // Defaulting to DRAFT since 'status' column is not available
        payment_status: invoiceData && 'payment_status' in invoiceData ? invoiceData.payment_status as PaymentStatus : PaymentStatus.UNPAID, // Defaulting to UNPAID if not available
        payment_method: invoiceData.payment_method,
        customer_name: invoiceData.customer_name,
        supplier_name: invoiceData.supplier_name,
        warehouse_name: invoiceData.warehouse_name,
        subtotal: invoiceData.subtotal || 0,
        discount_amount: invoiceData.discount_amount,
        discount_percent: invoiceData.discount_percent || 0,
        tax_amount: invoiceData.tax_amount,
        tax_percent: invoiceData.tax_percent || 0,
        total_amount: invoiceData.total_amount,
        paid_amount: invoiceData.paid_amount,
        remaining_amount: invoiceData.remaining_amount,
        notes: invoiceData.notes,
        return_reason: invoiceData.return_reason,
        returned_from_invoice_id: invoiceData.returned_from_invoice_id,
        original_invoice_number: invoiceData.original_invoice_number,
      };
      
      // Set state
      setInvoice(transformedInvoice);
      setItems(itemsData || []);
      setPaymentHistory([]); // Setting empty array since we can't access payment_transactions
      
      // Fetch additional related data if needed
      if (isReturnInvoice && invoiceData.returned_from_invoice_id) {
        await fetchOriginalInvoiceInfo(invoiceData.returned_from_invoice_id, invoiceType);
      }
      
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل بيانات الفاتورة",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
    const fetchOriginalInvoiceInfo = async (originalInvoiceId: string, returnType: InvoiceType) => {
    try {
      const { data, error } = await supabase
        .from('invoices')
        .select('invoice_number')
        .eq('id', originalInvoiceId)
        .single();
      
      if (error) throw error;
      
      if (data) {
        setInvoice(prev => ({
          ...prev,
          original_invoice_number: data.invoice_number
        }));
      }
    } catch (error) {
      console.error('Error fetching original invoice info:', error);
    }
  };

  const getStatusBadge = (status: InvoiceStatus) => {
    switch (status) {
      case InvoiceStatus.DRAFT:
        return <Badge variant="outline">مسودة</Badge>;
      case InvoiceStatus.COMPLETED:
        return <Badge variant="default" className="bg-green-500">مكتملة</Badge>;
      case InvoiceStatus.CANCELED:
        return <Badge variant="destructive">ملغية</Badge>;
      case InvoiceStatus.RETURNED:
        return <Badge variant="secondary">مرتجعة</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPaymentStatusBadge = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PAID:
        return <Badge variant="default" className="bg-green-500">مدفوعة</Badge>;
      case PaymentStatus.PARTIAL:
        return <Badge variant="default" className="bg-amber-500">مدفوعة جزئيًا</Badge>;
      case PaymentStatus.UNPAID:
        return <Badge variant="destructive">غير مدفوعة</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPaymentMethodText = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return "نقداً";
      case PaymentMethod.BANK_CARD:
        return "بطاقة بنكية";
      case PaymentMethod.CREDIT:
        return "آجل";
      case PaymentMethod.MIXED:
        return "مختلط";
      default:
        return method;
    }
  };

  const handleEditInvoice = () => {
    let route = '';
    
    switch (invoiceType) {
      case InvoiceType.SALES:
        route = `/invoices/sales/edit/${invoiceId}`;
        break;
      case InvoiceType.PURCHASE:
        route = `/invoices/purchases/edit/${invoiceId}`;
        break;
      case InvoiceType.SALES_RETURN:
        route = `/invoices/sales-returns/edit/${invoiceId}`;
        break;
      case InvoiceType.PURCHASE_RETURN:
        route = `/invoices/purchase-returns/edit/${invoiceId}`;
        break;
    }
    
    navigate(route);
  };

  const handleCreateReturn = () => {
    let route = '';
    
    if (invoiceType === InvoiceType.SALES) {
      route = `/invoices/sales-returns/new?originalId=${invoiceId}`;
    } else if (invoiceType === InvoiceType.PURCHASE) {
      route = `/invoices/purchase-returns/new?originalId=${invoiceId}`;
    }
    
    navigate(route);
  };

  const printInvoice = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة ${invoice?.invoice_number}</title>
        <style>
          @media print {
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              direction: rtl;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
            }
            .invoice-details {
              margin-bottom: 20px;
            }
            .customer-details {
              margin-bottom: 20px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: right;
            }
            .total-section {
              text-align: left;
              margin-top: 20px;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              font-size: 12px;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>فاتورة رقم: ${invoice?.invoice_number}</h1>
          <p>التاريخ: ${new Date(invoice?.created_at || '').toLocaleDateString('ar')}</p>
        </div>
        
        <div class="customer-details">
          <p><strong>${
            invoiceType === InvoiceType.SALES || invoiceType === InvoiceType.SALES_RETURN ? 'العميل' : 'المورد'
          }:</strong> ${invoice?.customer_name || invoice?.supplier_name}</p>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>المنتج</th>
              <th>الكمية</th>
              <th>السعر</th>
              <th>الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            ${items.map((item, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.unit_price.toLocaleString('ar')} د.ج</td>
                <td>${item.total_price.toLocaleString('ar')} د.ج</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="total-section">
          <p><strong>المجموع الفرعي:</strong> ${invoice?.subtotal.toLocaleString('ar')} د.ج</p>
          ${invoice?.discount_amount ? `<p><strong>الخصم:</strong> ${invoice.discount_amount.toLocaleString('ar')} د.ج</p>` : ''}
          ${invoice?.tax_amount ? `<p><strong>الضريبة:</strong> ${invoice.tax_amount.toLocaleString('ar')} د.ج</p>` : ''}
          <p><strong>المجموع النهائي:</strong> ${invoice?.total_amount.toLocaleString('ar')} د.ج</p>
          <p><strong>المبلغ المدفوع:</strong> ${invoice?.paid_amount.toLocaleString('ar')} د.ج</p>
          ${invoice?.remaining_amount > 0 ? `<p><strong>المبلغ المتبقي:</strong> ${invoice.remaining_amount.toLocaleString('ar')} د.ج</p>` : ''}
        </div>
        
        <div class="footer">
          <p>شكراً لتعاملكم معنا</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">جاري التحميل...</div>;
  }

  if (!invoice) {
    return <div className="text-center p-8">لم يتم العثور على الفاتورة</div>;
  }

  return (
    <div className="space-y-6">
      {/* Top action bar */}
      <div className="flex flex-wrap justify-between gap-4">
        <Button
          variant="outline"
          onClick={() => navigate('/invoices')}
        >
          <ArrowLeft className="w-4 h-4 ml-2" />
          عودة إلى القائمة
        </Button>

        <div className="flex gap-2">
          <PDFDownloadLink 
            document={
              <PDFDocument 
                invoice={invoice} 
                items={items} 
                invoiceType={invoiceType} 
              />
            } 
            fileName={`invoice-${invoice.invoice_number}.pdf`}
          >
            {({ loading }) => (
              <Button variant="outline" disabled={loading}>
                <FileDown className="w-4 h-4 ml-2" />
                تحميل PDF
              </Button>
            )}
          </PDFDownloadLink>

          <Button onClick={printInvoice}>
            <Printer className="w-4 h-4 ml-2" />
            طباعة
          </Button>

          {invoice.status === InvoiceStatus.DRAFT && (
            <Button variant="outline" onClick={handleEditInvoice}>
              <Edit className="w-4 h-4 ml-2" />
              تعديل
            </Button>
          )}

          {(invoiceType === InvoiceType.SALES || invoiceType === InvoiceType.PURCHASE) && 
          invoice.status === InvoiceStatus.COMPLETED && (
            <Button variant="secondary" onClick={handleCreateReturn}>
              <RotateCcw className="w-4 h-4 ml-2" />
              إنشاء إرجاع
            </Button>
          )}
        </div>
      </div>

      {/* Invoice header */}
      <Card>
        <CardHeader>
          <div className="flex flex-wrap justify-between items-center">
            <div>
              <CardTitle className="text-xl mb-2 flex items-center">
                {invoiceType === InvoiceType.SALES && (
                  <Receipt className="w-5 h-5 ml-2 text-primary" />
                )}
                {invoiceType === InvoiceType.PURCHASE && (
                  <ShoppingBag className="w-5 h-5 ml-2 text-primary" />
                )}
                {invoiceType === InvoiceType.SALES_RETURN && (
                  <RotateCcw className="w-5 h-5 ml-2 text-primary" />
                )}
                {invoiceType === InvoiceType.PURCHASE_RETURN && (
                  <RotateCcw className="w-5 h-5 ml-2 text-primary" />
                )}
                
                {invoiceType === InvoiceType.SALES && 'فاتورة مبيعات'}
                {invoiceType === InvoiceType.PURCHASE && 'فاتورة مشتريات'}
                {invoiceType === InvoiceType.SALES_RETURN && 'فاتورة إرجاع مبيعات'}
                {invoiceType === InvoiceType.PURCHASE_RETURN && 'فاتورة إرجاع مشتريات'}
                
                <div className="mx-2 text-gray-400">|</div>
                <span className="text-lg font-normal">{invoice.invoice_number}</span>
              </CardTitle>
              
              <div className="flex flex-wrap gap-6 text-sm text-gray-500">
                <div>
                  <span className="ml-1">التاريخ:</span>
                  {format(new Date(invoice.created_at), 'dd MMMM yyyy', { locale: ar })}
                </div>
                
                {invoice.reference_number && (
                  <div>
                    <span className="ml-1">الرقم المرجعي:</span>
                    {invoice.reference_number}
                  </div>
                )}
                
                {isReturnInvoice && invoice.original_invoice_number && (
                  <div>
                    <span className="ml-1">الفاتورة الأصلية:</span>
                    {invoice.original_invoice_number}
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex flex-col items-end gap-2 mt-2 sm:mt-0">
              <div className="flex gap-2">
                {getStatusBadge(invoice.status)}
                {getPaymentStatusBadge(invoice.payment_status)}
              </div>              {/* Remove reference to non-existent created_by field */}
              <div className="text-sm text-gray-500">
                تم إنشاؤها: {format(new Date(invoice.created_at), 'dd MMMM yyyy', { locale: ar })}
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium mb-2">
                {isSalesInvoice ? 'بيانات العميل' : 'بيانات المورد'}
              </h3>
              <div className="bg-gray-50 p-3 rounded-md">
                <div className="font-medium">
                  {isSalesInvoice ? invoice.customer_name : invoice.supplier_name || 'غير محدد'}
                </div>
                {/* Additional customer/supplier details could be shown here */}
              </div>
            </div>
            
            {(invoiceType === InvoiceType.PURCHASE || invoiceType === InvoiceType.PURCHASE_RETURN) && (
              <div>
                <h3 className="text-sm font-medium mb-2">المستودع</h3>
                <div className="bg-gray-50 p-3 rounded-md">
                  <div>{invoice.warehouse_name || 'غير محدد'}</div>
                </div>
              </div>
            )}
            
            <div>
              <h3 className="text-sm font-medium mb-2">طريقة الدفع</h3>
              <div className="bg-gray-50 p-3 rounded-md">
                <div className="flex items-center">
                  {invoice.payment_method === PaymentMethod.CASH && (
                    <DollarSign className="w-4 h-4 ml-1" />
                  )}
                  {invoice.payment_method === PaymentMethod.BANK_CARD && (
                    <CreditCard className="w-4 h-4 ml-1" />
                  )}
                  {invoice.payment_method === PaymentMethod.CREDIT && (
                    <Clock className="w-4 h-4 ml-1" />
                  )}
                  {invoice.payment_method === PaymentMethod.MIXED && (
                    <DollarSign className="w-4 h-4 ml-1" />
                  )}
                  {getPaymentMethodText(invoice.payment_method)}
                </div>
              </div>
            </div>
            
            {isReturnInvoice && invoice.return_reason && (
              <div className="md:col-span-3">
                <h3 className="text-sm font-medium mb-2">سبب الإرجاع</h3>
                <div className="bg-gray-50 p-3 rounded-md">
                  {invoice.return_reason}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Invoice items */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">بنود الفاتورة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">#</TableHead>
                  <TableHead>المنتج</TableHead>
                  <TableHead className="text-center">السعر</TableHead>
                  <TableHead className="text-center">الكمية</TableHead>
                  <TableHead className="text-center">الخصم</TableHead>
                  <TableHead className="text-right">الإجمالي</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      <div className="font-medium">{item.name}</div>
                      {item.barcode && (
                        <div className="text-xs text-gray-500">{item.barcode}</div>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      {formatCurrency(item.unit_price)} دج
                    </TableCell>
                    <TableCell className="text-center">
                      {item.quantity}
                    </TableCell>
                    <TableCell className="text-center">
                      {item.discount_percent > 0 ? (
                        <>
                          {item.discount_percent}% 
                          <span className="block text-xs text-gray-500">
                            ({formatCurrency(item.discount_amount)} دج)
                          </span>
                        </>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(item.total_price)} دج
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Invoice summary and payment info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Invoice summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">ملخص الفاتورة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>{formatCurrency(invoice.subtotal)} دج</span>
              </div>
              
              {invoice.discount_amount > 0 && (
                <div className="flex justify-between">
                  <span>الخصم ({invoice.discount_percent}%):</span>
                  <span className="text-red-500">- {formatCurrency(invoice.discount_amount)} دج</span>
                </div>
              )}
              
              {invoice.tax_amount > 0 && (
                <div className="flex justify-between">
                  <span>الضريبة ({invoice.tax_percent}%):</span>
                  <span>{formatCurrency(invoice.tax_amount)} دج</span>
                </div>
              )}
              
              <div className="flex justify-between pt-2 border-t font-bold">
                <span>الإجمالي:</span>
                <span>{formatCurrency(invoice.total_amount)} دج</span>
              </div>
              
              <div className="flex justify-between pt-2 border-t">
                <span>المدفوع:</span>
                <span className="text-green-600">{formatCurrency(invoice.paid_amount)} دج</span>
              </div>
              
              {invoice.payment_status !== PaymentStatus.PAID && (
                <div className="flex justify-between">
                  <span>المتبقي:</span>
                  <span className="text-red-500">{formatCurrency(invoice.remaining_amount)} دج</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Payment history */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">سجل الدفعات</CardTitle>
          </CardHeader>
          <CardContent>
            {paymentHistory.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                لا توجد معاملات مالية مسجلة
              </div>
            ) : (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>طريقة الدفع</TableHead>
                      <TableHead className="text-right">المبلغ</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentHistory.map((payment, index) => (
                      <TableRow key={index}>
                        <TableCell>{format(new Date(payment.payment_date), 'dd/MM/yyyy')}</TableCell>
                        <TableCell>{getPaymentMethodText(payment.payment_method)}</TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(payment.amount)} دج
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

        {/* Notes */}
        {invoice.notes && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">ملاحظات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                {invoice.notes}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

export default InvoiceDetails;
