
import { Layout } from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ShoppingCart, Scan, Plus, Trash2, Calculator, CreditCard, Banknote } from "lucide-react";
import { formatCurrency } from "@/utils/currency";

const POS = () => {
  const cartItems = [
    { id: 1, name: "منتج تجريبي 1", price: 2500, quantity: 2, total: 5000 },
    { id: 2, name: "منتج تجريبي 2", price: 1800, quantity: 1, total: 1800 },
  ];

  const subtotal = 6800;
  const tax = 1156;
  const total = 7956;

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <ShoppingCart className="w-8 h-8 text-primary" />
            نقطة البيع
          </h1>
          <Badge variant="outline" className="text-sm">
            الكاشير: أحمد محمد
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* منطقة البحث والمنتجات */}
          <div className="lg:col-span-2 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Scan className="w-5 h-5" />
                  بحث المنتجات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  <Input 
                    placeholder="اسكن الباركود أو ابحث عن المنتج..."
                    className="flex-1"
                  />
                  <Button>
                    <Scan className="w-4 h-4 ml-2" />
                    مسح
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* شبكة المنتجات السريعة */}
            <Card>
              <CardHeader>
                <CardTitle>المنتجات السريعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                    <Card key={item} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-3 text-center">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto mb-2"></div>
                        <p className="text-sm font-medium">منتج {item}</p>
                        <p className="text-xs text-gray-500">{formatCurrency(1500 + item * 100)}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* سلة التسوق والحساب */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5" />
                  سلة التسوق
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex-1">
                      <p className="font-medium text-sm">{item.name}</p>
                      <p className="text-xs text-gray-500">
                        {formatCurrency(item.price)} × {item.quantity}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <p className="font-medium">{formatCurrency(item.total)}</p>
                      <Button size="sm" variant="outline">
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>المجموع الفرعي:</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>الضريبة (17%):</span>
                    <span>{formatCurrency(tax)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>الإجمالي:</span>
                    <span className="text-primary">{formatCurrency(total)}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Button className="w-full" size="lg">
                    <CreditCard className="w-4 h-4 ml-2" />
                    دفع بالبطاقة
                  </Button>
                  <Button variant="outline" className="w-full" size="lg">
                    <Banknote className="w-4 h-4 ml-2" />
                    دفع نقدي
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default POS;
