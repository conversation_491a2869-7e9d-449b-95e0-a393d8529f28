
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";

interface InvoiceItem {
  product_id: string;
  product_name: string; // اسم المنتج الذي يُعرض في الواجهة
  name?: string; // الاسم المستخدم في قاعدة البيانات
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface AddInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInvoiceAdded: () => void;
}

export function AddInvoiceDialog({ open, onOpenChange, onInvoiceAdded }: AddInvoiceDialogProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    invoice_number: "",
    customer_id: "",
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: "",
    type: "sales",
    payment_method: "cash",
    notes: ""
  });
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [quantity, setQuantity] = useState(1);

  // توليد رقم فاتورة تلقائي عند فتح الحوار
  const generateInvoiceNumber = async () => {
    const { data: lastInvoice } = await supabase
      .from('invoices')
      .select('invoice_number')
      .order('created_at', { ascending: false })
      .limit(1);

    let nextNumber = 1;
    if (lastInvoice && lastInvoice.length > 0) {
      const lastNumber = parseInt(lastInvoice[0].invoice_number.replace(/\D/g, '')) || 0;
      nextNumber = lastNumber + 1;
    }

    return `INV-${nextNumber.toString().padStart(4, '0')}`;
  };

  // جلب العملاء
  const { data: customers } = useQuery({
    queryKey: ['customers'],
    queryFn: async () => {
      const { data, error } = await supabase.from('customers').select('*');
      if (error) throw error;
      return data;
    }
  });

  // جلب المنتجات
  const { data: products } = useQuery({
    queryKey: ['products'],
    queryFn: async () => {
      const { data, error } = await supabase.from('products').select('*');
      if (error) throw error;
      return data;
    }
  });
  // تعيين رقم فاتورة تلقائي عند فتح الحوار
  useEffect(() => {
    if (open && !formData.invoice_number) {
      generateInvoiceNumber().then(invoiceNumber => {
        setFormData(prev => ({ ...prev, invoice_number: invoiceNumber }));
      });
    }
  }, [open, formData.invoice_number]);

  const addItem = () => {
    const product = products?.find(p => p.id === selectedProduct);
    if (!product || quantity <= 0) return;

    const newItem: InvoiceItem = {
      product_id: product.id,
      product_name: product.name,
      quantity,
      unit_price: product.selling_price,
      total_price: quantity * product.selling_price
    };

    setItems([...items, newItem]);
    setSelectedProduct("");
    setQuantity(1);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const getTotalAmount = () => {
    return items.reduce((sum, item) => sum + item.total_price, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.invoice_number || !formData.customer_id || items.length === 0) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة وإضافة منتج واحد على الأقل",
        variant: "destructive",
      });
      return;
    }    try {
      // إعداد بيانات الفاتورة مع معالجة تاريخ الاستحقاق واستخدام القيم الصحيحة للـ enum
      const invoiceData = {
        ...formData,
        due_date: formData.due_date || null,
        // حذف حقل subtotal لأنه غير موجود في قاعدة البيانات
        discount_amount: 0,
        discount_percent: 0,
        tax_amount: 0,
        tax_rate: 0,
        total_amount: getTotalAmount(),
        remaining_amount: getTotalAmount(),
        paid_amount: 0,
        payment_status: 'unpaid' as const // استخدام قيمة صحيحة من enum
      };

      console.log('Invoice data to be sent:', invoiceData);

      // إضافة الفاتورة
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert([invoiceData])
        .select()
        .single();

      if (invoiceError) {
        console.error('Invoice error:', invoiceError);
        let errorMessage = "حدث خطأ في إضافة الفاتورة";
        
        // تحسين رسائل الخطأ لتكون أكثر وضوحاً
        if (invoiceError.code === '23503') {
          errorMessage = "خطأ في العلاقات: تأكد من صحة العميل المحدد";
        } else if (invoiceError.code === '23502') {
          errorMessage = "بيانات مطلوبة غير موجودة: تأكد من ملء جميع الحقول الإلزامية";
        } else if (invoiceError.message) {
          errorMessage += `: ${invoiceError.message}`;
        }
        
        toast({
          title: "خطأ",
          description: errorMessage,
          variant: "destructive",
        });
        return; // نخرج من الدالة بدلاً من إلقاء الخطأ
      }      try {        // إضافة عناصر الفاتورة
        const invoiceItems = items.map(item => ({
          invoice_id: invoice.id,
          product_id: item.product_id,
          // حذف حقل name لأنه غير موجود في جدول invoice_items
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price
        }));

        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(invoiceItems);        if (itemsError) {
          console.error('Items error:', itemsError);
          let errorDescription = "تم إضافة الفاتورة لكن حدث خطأ في إضافة المنتجات";
          
          // إضافة المزيد من التفاصيل إلى رسالة الخطأ
          if (itemsError.message) {
            console.log('Error details:', itemsError.message);
            errorDescription += `: ${itemsError.message}`;
          }
          if (itemsError.code) {
            console.log('Error code:', itemsError.code);
            errorDescription += ` (كود الخطأ: ${itemsError.code})`;
          }
          
          toast({
            title: "تنبيه",
            description: errorDescription,
            variant: "destructive",
          });
        }

        // تحديث المخزون
        let stockUpdateFailed = false;
        for (const item of items) {
          const product = products?.find(p => p.id === item.product_id);
          if (product) {
            const { error: stockError } = await supabase
              .from('products')
              .update({ 
                stock_quantity: product.stock_quantity - item.quantity 
              })
              .eq('id', item.product_id);

            if (stockError) {
              console.error('Stock update error:', stockError);
              stockUpdateFailed = true;
            }
          }
        }

        if (stockUpdateFailed) {          toast({
            title: "تنبيه",
            description: "تم إضافة الفاتورة لكن حدث خطأ في تحديث المخزون",
            variant: "destructive",
          });
        } else {
          toast({
            title: "تم بنجاح",
            description: "تم إضافة الفاتورة الجديدة بنجاح",
          });
        }

        // إعادة تعيين النموذج
        const newInvoiceNumber = await generateInvoiceNumber();
        setFormData({
          invoice_number: newInvoiceNumber,
          customer_id: "",
          invoice_date: new Date().toISOString().split('T')[0],
          due_date: "",
          type: "sales",
          payment_method: "cash",
          notes: ""
        });
        setItems([]);
        onInvoiceAdded();
        onOpenChange(false);
      
      } catch (itemProcessingError) {
        console.error('Error processing invoice items:', itemProcessingError);        toast({
          title: "تنبيه",
          description: "تم إضافة الفاتورة لكن حدث خطأ في معالجة المنتجات أو المخزون",
          variant: "destructive",
        });
        
        // حتى مع وجود خطأ في المنتجات، نقوم بإغلاق النافذة وتحديث القائمة لأن الفاتورة تم إنشاؤها بنجاح
        onInvoiceAdded();
        onOpenChange(false);
      }    } catch (error) {
      console.error('Error creating invoice:', error);
      
      // تحسين رسائل الخطأ لتكون أكثر تفصيلاً وفائدة
      let errorMessage = "حدث خطأ في إضافة الفاتورة. يرجى المحاولة مرة أخرى.";
      
      if (error && typeof error === 'object') {
        // إضافة رسالة الخطأ إذا كانت متاحة
        if ('message' in error && typeof error.message === 'string') {
          errorMessage = `حدث خطأ في إضافة الفاتورة: ${error.message}`;
        }
        
        // إضافة تفاصيل إضافية إذا كانت متاحة
        if ('details' in error && typeof error.details === 'string') {
          console.log('Error details:', error.details);
          errorMessage += ` - ${error.details}`;
        }
        
        // إضافة كود الخطأ إذا كان متاحًا
        if ('code' in error && error.code) {
          console.log('Error code:', error.code);
          errorMessage += ` (كود الخطأ: ${error.code})`;
        }
      }
      
      toast({
        title: "خطأ",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">        <DialogHeader>
          <DialogTitle>إضافة فاتورة جديدة</DialogTitle>
          <DialogDescription>
            قم بإدخال معلومات الفاتورة والمنتجات المراد إضافتها
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* معلومات الفاتورة الأساسية */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="invoice_number">رقم الفاتورة *</Label>
              <Input
                id="invoice_number"
                value={formData.invoice_number}
                onChange={(e) => setFormData({...formData, invoice_number: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="customer_id">العميل *</Label>
              <Select value={formData.customer_id} onValueChange={(value) => setFormData({...formData, customer_id: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر العميل" />
                </SelectTrigger>
                <SelectContent>
                  {customers?.map(customer => (
                    <SelectItem key={customer.id} value={customer.id}>{customer.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="invoice_date">تاريخ الفاتورة</Label>
              <Input
                id="invoice_date"
                type="date"
                value={formData.invoice_date}
                onChange={(e) => setFormData({...formData, invoice_date: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="due_date">تاريخ الاستحقاق</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => setFormData({...formData, due_date: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="payment_method">طريقة الدفع</Label>
              <Select value={formData.payment_method} onValueChange={(value) => setFormData({...formData, payment_method: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">نقدي</SelectItem>
                  <SelectItem value="bank_card">بطاقة بنكية</SelectItem>
                  <SelectItem value="credit">آجل</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* إضافة المنتجات */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">المنتجات</h3>
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <Label>المنتج</Label>
                <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المنتج" />
                  </SelectTrigger>
                  <SelectContent>
                    {products?.map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name} - {product.selling_price} ريال (متوفر: {product.stock_quantity})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-32">
                <Label>الكمية</Label>
                <Input
                  type="number"
                  min="1"
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                />
              </div>
              <Button type="button" onClick={addItem}>
                <Plus className="w-4 h-4" />
              </Button>
            </div>

            {/* جدول المنتجات المضافة */}
            {items.length > 0 && (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>المنتج</TableHead>
                    <TableHead>الكمية</TableHead>
                    <TableHead>السعر</TableHead>
                    <TableHead>الإجمالي</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.product_name}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{item.unit_price.toLocaleString()} ريال</TableCell>
                      <TableCell>{item.total_price.toLocaleString()} ريال</TableCell>
                      <TableCell>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(index)}
                        >
                          <Trash2 className="w-4 h-4 text-red-500" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell colSpan={3} className="font-bold">الإجمالي</TableCell>
                    <TableCell className="font-bold">{getTotalAmount().toLocaleString()} ريال</TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            )}
          </div>

          {/* ملاحظات */}
          <div>
            <Label htmlFor="notes">ملاحظات</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              rows={2}
            />
          </div>

          {/* أزرار التحكم */}
          <div className="flex gap-2 pt-4">
            <Button type="submit">إنشاء الفاتورة</Button>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              إلغاء
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
