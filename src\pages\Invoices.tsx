import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Eye, FileEdit, Download, FileSpreadsheet } from "lucide-react";
import { formatCurrency } from "@/utils/currency";
import { ViewInvoiceDialog } from "@/components/ViewInvoiceDialog";
import { EditInvoiceDialog } from "@/components/EditInvoiceDialog";

export default function Invoices() {
  // حالة للفلاتر ومربع البحث
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");

  // حالة للفاتورة المختارة وحالة الحوارات
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // استعلام لجلب الفواتير
  const { data: invoices, isLoading, refetch } = useQuery({
    queryKey: ["invoices"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("invoices")
        .select(`
          *,
          customers (name),
          invoice_items (*)
        `)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  // وظيفة لتصفية الفواتير حسب معايير البحث
  const filteredInvoices = invoices?.filter((invoice) => {
    // البحث في رقم الفاتورة واسم العميل
    const searchMatch =
      searchQuery === "" ||
      invoice.invoice_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (invoice.customers?.name &&
        invoice.customers.name.toLowerCase().includes(searchQuery.toLowerCase()));    // تصفية حسب حالة الدفع
    const statusMatch = statusFilter === "all" || invoice.payment_status === statusFilter;

    // تصفية حسب التاريخ
    let dateMatch = true;
    if (dateFilter && dateFilter !== "all") {
      const today = new Date();
      const invoiceDate = new Date(invoice.invoice_date);switch (dateFilter) {
        case "today": {
          dateMatch =
            invoiceDate.getDate() === today.getDate() &&
            invoiceDate.getMonth() === today.getMonth() &&
            invoiceDate.getFullYear() === today.getFullYear();
          break;
        }
        case "this_week": {
          const startOfWeek = new Date(today);
          startOfWeek.setDate(today.getDate() - today.getDay());
          dateMatch = invoiceDate >= startOfWeek && invoiceDate <= today;
          break;
        }
        case "this_month": {
          dateMatch =
            invoiceDate.getMonth() === today.getMonth() &&
            invoiceDate.getFullYear() === today.getFullYear();
          break;
        }
        case "this_year": {
          dateMatch = invoiceDate.getFullYear() === today.getFullYear();
          break;
        }
      }
    }

    return searchMatch && statusMatch && dateMatch;
  });

  const getStatusClass = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "partial":
        return "bg-yellow-100 text-yellow-800";
      case "unpaid":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "paid":
        return "مدفوع";
      case "partial":
        return "مدفوع جزئياً";
      case "unpaid":
        return "غير مدفوع";
      default:
        return status;
    }
  };

  // وظائف معالجة الأحداث للأزرار
  const handleViewInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setIsViewDialogOpen(true);
  };

  const handleEditInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setIsEditDialogOpen(true);
  };
  interface Invoice {
    id: string;
    invoice_number: string;
    invoice_date: string;
    due_date?: string | null;
    total_amount: number;
    paid_amount: number;
    remaining_amount: number;
    payment_status: string;
    payment_method: string | null;
    notes?: string | null;
    customers?: {
      name: string;
      phone?: string;
      email?: string | null;
    };
    invoice_items?: Array<{
      quantity: number;
      unit_price: number;
      total_price: number;
      products?: {
        name: string;
        code?: string;
      };
    }>;
  }

  const handleDownloadInvoice = async (invoice: Invoice) => {
    try {
      // استدعاء وظيفة تحميل ملف PDF
      const { downloadInvoicePDF } = await import("@/utils/pdf-generator");
      const result = await downloadInvoicePDF(invoice);
      
      if (!result) {
        throw new Error("فشل إنشاء ملف PDF");
      }
    } catch (error) {
      console.error("Error downloading invoice:", error);
      alert("حدث خطأ أثناء تحميل الفاتورة");
    }
  };

  const handleExportToExcel = async () => {
    try {
      if (!filteredInvoices || filteredInvoices.length === 0) {
        alert("لا توجد فواتير لتصديرها");
        return;
      }
      
      // استدعاء وظيفة تصدير الفواتير إلى Excel
      const { exportInvoicesToExcel } = await import("@/utils/excel-export");
      exportInvoicesToExcel(filteredInvoices);
    } catch (error) {
      console.error("Error exporting invoices to Excel:", error);
      alert("حدث خطأ أثناء تصدير الفواتير");
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">الفواتير</h2>
        <Button onClick={handleExportToExcel}>
          <FileSpreadsheet className="ml-2 h-4 w-4" />
          تصدير إلى Excel
        </Button>
      </div>

      {/* فلاتر البحث */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Input
            placeholder="بحث عن فاتورة..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>        <div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="حالة الدفع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="paid">مدفوع</SelectItem>
              <SelectItem value="partial">مدفوع جزئياً</SelectItem>
              <SelectItem value="unpaid">غير مدفوع</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger>
              <SelectValue placeholder="التاريخ" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">كل الفترات</SelectItem>
              <SelectItem value="today">اليوم</SelectItem>
              <SelectItem value="this_week">هذا الأسبوع</SelectItem>
              <SelectItem value="this_month">هذا الشهر</SelectItem>
              <SelectItem value="this_year">هذه السنة</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* جدول الفواتير */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">رقم الفاتورة</TableHead>
              <TableHead className="text-right">العميل</TableHead>
              <TableHead className="text-right">التاريخ</TableHead>
              <TableHead className="text-right">المبلغ</TableHead>
              <TableHead className="text-right">المدفوع</TableHead>
              <TableHead className="text-right">المتبقي</TableHead>
              <TableHead className="text-right">حالة الدفع</TableHead>
              <TableHead className="w-[180px]">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-10">
                  جاري التحميل...
                </TableCell>
              </TableRow>
            ) : filteredInvoices && filteredInvoices.length > 0 ? (
              filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                  <TableCell>{invoice.customers?.name || "غير محدد"}</TableCell>
                  <TableCell>{new Date(invoice.invoice_date).toLocaleDateString("ar-SA")}</TableCell>
                  <TableCell>{formatCurrency(invoice.total_amount)}</TableCell>
                  <TableCell>{formatCurrency(invoice.paid_amount)}</TableCell>
                  <TableCell>{formatCurrency(invoice.remaining_amount)}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(
                        invoice.payment_status
                      )}`}
                    >
                      {getStatusText(invoice.payment_status)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleViewInvoice(invoice.id)}
                        title="عرض"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleEditInvoice(invoice.id)}
                        title="تعديل"
                      >
                        <FileEdit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleDownloadInvoice(invoice)}
                        title="تحميل PDF"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-10 text-muted-foreground">
                  لا توجد فواتير متطابقة مع معايير البحث
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* مكونات الحوارات */}
      <ViewInvoiceDialog
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
        invoiceId={selectedInvoiceId}
      />
      
      <EditInvoiceDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        invoiceId={selectedInvoiceId}
        onInvoiceUpdated={refetch}
      />
    </div>
  );
}