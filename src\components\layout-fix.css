/* Layout adjustments for the top navbar */
.min-h-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Top navbar spacing */
.top-navbar + main {
  padding-top: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  main {
    padding: 0.75rem !important;
  }
}

/* Smooth transitions */
.min-h-screen > * {
  transition: all 0.3s ease;
}

/* Print styles */
@media print {
  .top-navbar {
    display: none;
  }

  main {
    padding: 0 !important;
  }
}
