
import { Layout } from "@/components/Layout";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar, TrendingUp, TrendingDown, ShoppingCart, Package, Users, Truck, Receipt, Wallet, DollarSign, BarChart3, Pie<PERSON>hart, LineChart, Download } from "lucide-react";
import { useState } from "react";

export default function Reports() {
  const [selectedPeriod, setSelectedPeriod] = useState("this_month");

  const reportCards = [
    {
      title: "تقارير المبيعات",
      description: "مبيعات يومية، أسبوعية، شهرية، وسنوية",
      icon: ShoppingCart,
      color: "bg-green-500",
      reports: ["المبيعات اليومية", "المبيعات الشهرية", "المبيعات حسب المنتج", "المبيعات حسب العميل"]
    },
    {
      title: "تقارير المخزون",
      description: "حالة المخزون وحركة المنتجات",
      icon: Package,
      color: "bg-blue-500",
      reports: ["الكميات الحالية", "المنتجات منخفضة المخزون", "المنتجات المنتهية", "حركة المخزون"]
    },
    {
      title: "تقارير الموردين",
      description: "أرصدة ومشتريات الموردين",
      icon: Truck,
      color: "bg-purple-500",
      reports: ["أرصدة الموردين", "المشتريات من الموردين", "المدفوعات والمتأخرات", "الإرجاعات"]
    },
    {
      title: "تقارير الفواتير",
      description: "فواتير البيع والشراء والإرجاع",
      icon: Receipt,
      color: "bg-orange-500",
      reports: ["فواتير البيع", "فواتير الشراء", "الفواتير الآجلة", "فواتير الإرجاع"]
    },
    {
      title: "تقارير المصاريف",
      description: "مصاريف حسب التصنيف والفترة",
      icon: Wallet,
      color: "bg-red-500",
      reports: ["المصاريف حسب التصنيف", "مقارنة المصاريف", "المصاريف الشهرية", "تحليل المصاريف"]
    },
    {
      title: "تقرير الربح والخسارة",
      description: "الإيرادات والمصاريف والربح الصافي",
      icon: TrendingUp,
      color: "bg-cyan-500",
      reports: ["الإيرادات الإجمالية", "تكلفة البضائع", "المصاريف التشغيلية", "الربح الصافي"]
    }
  ];

  const quickStats = [
    {
      title: "مبيعات اليوم",
      value: "15,750 ر.س",
      change: "+12.5%",
      icon: DollarSign,
      trend: "up"
    },
    {
      title: "إجمالي العملاء",
      value: "248",
      change: "+3.2%",
      icon: Users,
      trend: "up"
    },
    {
      title: "المنتجات النشطة",
      value: "1,247",
      change: "-2.1%",
      icon: Package,
      trend: "down"
    },
    {
      title: "طلبات معلقة",
      value: "23",
      change: "+5.4%",
      icon: Receipt,
      trend: "up"
    }
  ];

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            <p className="text-gray-500 mt-1">مراقبة الأداء وتحليل البيانات</p>
          </div>
          <div className="flex items-center gap-3">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="اختر الفترة الزمنية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="this_week">هذا الأسبوع</SelectItem>
                <SelectItem value="this_month">هذا الشهر</SelectItem>
                <SelectItem value="last_month">الشهر الماضي</SelectItem>
                <SelectItem value="this_year">هذا العام</SelectItem>
                <SelectItem value="custom">فترة مخصصة</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Download className="w-4 h-4 ml-2" />
              تصدير التقارير
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                    <div className={`flex items-center mt-2 text-sm ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4 ml-1" />
                      ) : (
                        <TrendingDown className="w-4 h-4 ml-1" />
                      )}
                      {stat.change}
                    </div>
                  </div>
                  <div className={`p-3 rounded-full ${
                    stat.trend === 'up' ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <stat.icon className={`w-6 h-6 ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reportCards.map((report, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-3 rounded-lg ${report.color}`}>
                    <report.icon className="w-6 h-6 text-white" />
                  </div>
                  <Badge variant="secondary">
                    {report.reports.length} تقرير
                  </Badge>
                </div>
                <CardTitle className="text-lg font-semibold">{report.title}</CardTitle>
                <p className="text-sm text-gray-500">{report.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 mb-4">
                  {report.reports.map((reportName, reportIndex) => (
                    <div key={reportIndex} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 cursor-pointer">
                      <span className="text-sm">{reportName}</span>
                      <Button variant="ghost" size="sm">
                        <BarChart3 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
                <Button className="w-full" variant="outline">
                  عرض جميع التقارير
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart Preview Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="w-5 h-5" />
                مبيعات الأسبوع الماضي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500">سيتم عرض الرسم البياني هنا</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                توزيع المبيعات حسب التصنيف
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <PieChart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500">سيتم عرض المخطط الدائري هنا</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
