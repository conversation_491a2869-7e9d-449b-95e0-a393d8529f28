import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Plus, 
  Trash2, 
  Save, 
  PrinterIcon,
  XCircle,
  Calculator,
  Search,
  Clock,
  Warehouse
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { 
  InvoiceType, 
  PaymentMethod, 
  InvoiceStatus, 
  PaymentStatus,
  InvoiceFormData,
} from "@/types/invoice";
// Extend InvoiceFormData interface to include reference_number
interface ExtendedInvoiceFormData extends InvoiceFormData {
  reference_number?: string;
  // tax_percent is already included in InvoiceFormData
}
import { toDbInvoice, toDbInvoiceItems, toDbPaymentTransaction } from "@/utils/database-helpers";
import { formatCurrency } from "@/utils/currency";

interface PurchaseInvoiceFormProps {
  invoiceId?: string;
  mode: 'new' | 'edit';
  onSaved?: () => void;
}

export function PurchaseInvoiceForm({ invoiceId, mode = 'new', onSaved }: PurchaseInvoiceFormProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  interface Supplier {
      id: string;
      name: string;
    }
  
  interface Warehouse {
      id: string;
      name: string;
    }
  
  interface Product {
    id: string;
    name: string;
    barcode?: string;
    purchase_price: number;
  }
  
    const [suppliers, setSuppliers] = useState<Supplier[]>([]);
    const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
    const [products, setProducts] = useState<Product[]>([]);
    const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
    const [searchTerm, setSearchTerm] = useState('');

  // Invoice form state
  const [invoice, setInvoice] = useState<ExtendedInvoiceFormData>({
    invoice_number: `PURCH-${new Date().getFullYear()}${new Date().getMonth() + 1}${new Date().getDate()}-${Math.floor(Math.random() * 1000)}`,
    reference_number: '',
    type: InvoiceType.PURCHASE,
    supplier_id: '',
    warehouse_id: '',
    subtotal: 0,
    discount_amount: 0,
    discount_percent: 0,
    tax_percent: 0,
    tax_amount: 0,
    tax_rate: 0,
    total_amount: 0,
    paid_amount: 0,
    remaining_amount: 0,
    status: InvoiceStatus.DRAFT,    payment_status: PaymentStatus.UNPAID,
    payment_method: PaymentMethod.CASH,
    notes: '',
    // Removed created_by as it's not in the database schema
    items: []
  });

  const [paymentDetails, setPaymentDetails] = useState({
    amount: 0,
    method: PaymentMethod.CASH,
    isPartial: false
  });

  // Load data
  useEffect(() => {
    fetchSuppliers();
    fetchWarehouses();
    fetchProducts();
    
    if (mode === 'edit' && invoiceId) {
      fetchInvoice(invoiceId);
    }
  }, [mode, invoiceId]);

  // Filter products based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
        (product.barcode && product.barcode.includes(searchTerm))
      );
      setFilteredProducts(filtered);
    }
  }, [searchTerm, products]);

  // Update totals whenever items or discounts change
  useEffect(() => {
    calculateInvoiceTotals();
  }, [invoice.items, invoice.discount_percent, invoice.tax_percent]);

  const fetchSuppliers = async () => {
    try {
      const { data, error } = await supabase
        .from('suppliers')
        .select('*')
        .order('name');
      
      if (error) throw error;
      setSuppliers(data || []);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل قائمة الموردين",
        variant: "destructive",
      });
    }
  };

  const fetchWarehouses = async () => {
    try {
      const { data, error } = await supabase
        .from('warehouses')
        .select('*')
        .order('name');
      
      if (error) throw error;
      setWarehouses(data || []);
    } catch (error) {
      console.error('Error fetching warehouses:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل قائمة المستودعات",
        variant: "destructive",
      });
    }
  };

  const fetchProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('name');
      
      if (error) throw error;
      setProducts(data || []);
      setFilteredProducts(data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل قائمة المنتجات",
        variant: "destructive",
      });
    }
  };

  const fetchInvoice = async (id: string) => {
    setIsLoading(true);
    try {      const { data, error } = await supabase
        .from('invoices')
        .select('*, items:invoice_items(*)')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      
      if (data) {
        setInvoice({
          ...data,
          items: data.items || []
        });
      }
    } catch (error) {
      console.error('Error fetching invoice:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل بيانات الفاتورة",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addProductToInvoice = (product: Product) => {
    // Check if product already exists in the invoice
    const existingItemIndex = invoice.items.findIndex(item => item.product_id === product.id);
    
    if (existingItemIndex >= 0) {
      // Update existing item's quantity
      const updatedItems = [...invoice.items];
      updatedItems[existingItemIndex].quantity += 1;
      updatedItems[existingItemIndex].total_price = 
        updatedItems[existingItemIndex].quantity * updatedItems[existingItemIndex].unit_price;
      
      setInvoice({
        ...invoice,
        items: updatedItems
      });
    } else {
      // Add new item - for purchases, we use purchase_price by default
      const newItem = {
        product_id: product.id,
        name: product.name,
        barcode: product.barcode,
        quantity: 1,
        unit_price: product.purchase_price,
        total_price: product.purchase_price,
        discount_amount: 0,
        discount_percent: 0,
        tax_percent: 0,
        tax_amount: 0
      };
      
      setInvoice({
        ...invoice,
        items: [...invoice.items, newItem]
      });
    }
  };

  const removeProductFromInvoice = (index: number) => {
    const updatedItems = [...invoice.items];
    updatedItems.splice(index, 1);
    
    setInvoice({
      ...invoice,
      items: updatedItems
    });
  };

  const updateProductQuantity = (index: number, quantity: number) => {
    if (quantity <= 0) return;
    
    const updatedItems = [...invoice.items];
    updatedItems[index].quantity = quantity;
    updatedItems[index].total_price = quantity * updatedItems[index].unit_price;
    
    setInvoice({
      ...invoice,
      items: updatedItems
    });
  };

  const updateProductPrice = (index: number, price: number) => {
    if (price <= 0) return;
    
    const updatedItems = [...invoice.items];
    updatedItems[index].unit_price = price;
    updatedItems[index].total_price = updatedItems[index].quantity * price;
    
    setInvoice({
      ...invoice,
      items: updatedItems
    });
  };

  const updateProductDiscount = (index: number, discountPercent: number) => {
    if (discountPercent < 0 || discountPercent > 100) return;
    
    const updatedItems = [...invoice.items];
    const item = updatedItems[index];
    
    item.discount_percent = discountPercent;
    item.discount_amount = (item.unit_price * item.quantity) * (discountPercent / 100);
    item.total_price = (item.unit_price * item.quantity) - item.discount_amount;
    
    setInvoice({
      ...invoice,
      items: updatedItems
    });
  };

  const calculateInvoiceTotals = useCallback(() => {
    const subtotal = invoice.items.reduce((sum, item) => sum + (item.total_price || 0), 0);
    
    // Calculate global discount
    const discountAmount = invoice.discount_percent ? subtotal * (invoice.discount_percent / 100) : (invoice.discount_amount || 0);
    
    // Calculate tax on subtotal after discount
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = invoice.tax_percent ? afterDiscount * (invoice.tax_percent / 100) : 0;
    
    // Calculate total
    const total = afterDiscount + taxAmount;
    
    setInvoice(prev => ({
      ...prev,
      subtotal,
      discount_amount: discountAmount,
      tax_amount: taxAmount,
      total_amount: total,
      remaining_amount: Math.max(0, total - prev.paid_amount)
    }));
    
    // Update payment amount if not in partial payment mode
    if (!paymentDetails.isPartial) {
      setPaymentDetails(prev => ({
        ...prev,
        amount: total
      }));
    }
  }, [invoice.items, invoice.discount_percent, invoice.tax_percent, invoice.paid_amount, paymentDetails.isPartial]);

  const handlePaymentMethodChange = (method: PaymentMethod) => {
    setPaymentDetails({
      ...paymentDetails,
      method
    });
    
    // If payment method is credit, set paid amount to 0
    if (method === PaymentMethod.CREDIT) {
      setPaymentDetails(prev => ({
        ...prev,
        isPartial: true,
        amount: 0
      }));
      
      setInvoice(prev => ({
        ...prev,
        paid_amount: 0,
        remaining_amount: prev.total_amount,
        payment_status: PaymentStatus.UNPAID,
        payment_method: PaymentMethod.CREDIT
      }));
    } else {
      setInvoice(prev => ({
        ...prev,
        payment_method: method
      }));
    }
  };

  const handlePaymentAmountChange = (amount: number) => {
    if (amount < 0) return;
    
    setPaymentDetails({
      ...paymentDetails,
      amount
    });
    
    // Update invoice payment status based on amount
    const paymentStatus = amount === 0 
      ? PaymentStatus.UNPAID 
      : amount >= invoice.total_amount 
        ? PaymentStatus.PAID 
        : PaymentStatus.PARTIAL;
    
    setInvoice(prev => ({
      ...prev,
      paid_amount: amount,
      remaining_amount: Math.max(0, prev.total_amount - amount),
      payment_status: paymentStatus,
      payment_method: amount === 0 ? PaymentMethod.CREDIT : paymentDetails.method
    }));
  };

  const handlePartialPaymentToggle = (isPartial: boolean) => {
    setPaymentDetails(prev => ({
      ...prev,
      isPartial
    }));
    
    if (!isPartial) {
      handlePaymentAmountChange(invoice.total_amount);
    }
  };

  const handleSupplierChange = (supplierId: string) => {
    // Find supplier name
    const supplier = suppliers.find(s => s.id === supplierId);
    
    setInvoice(prev => ({
      ...prev,
      supplier_id: supplierId,
      supplier_name: supplier?.name || ''
    }));
  };

  const saveInvoice = async (status: InvoiceStatus) => {
    // Validate invoice
    if (!invoice.supplier_id) {
      toast({
        title: "خطأ",
        description: "يجب تحديد المورد",
        variant: "destructive",
      });
      return;
    }
    
    if (invoice.items.length === 0) {
      toast({
        title: "خطأ",
        description: "يجب إضافة منتج واحد على الأقل إلى الفاتورة",
        variant: "destructive",
      });
      return;
    }
    
    if (!invoice.warehouse_id) {
      toast({
        title: "خطأ",
        description: "يجب تحديد المستودع",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      console.log("Saving purchase invoice with data:", invoice);
      
      // Use the helper function to create database compatible invoice object
      const invoiceData = toDbInvoice(invoice, status);        if (mode === 'new') {
        // Create new invoice
        const { data: invoiceResponse, error: invoiceError } = await supabase
          .from('invoices')
          .insert([invoiceData])
          .select()
          .single();
        
        if (invoiceError) {
          console.error("Error saving invoice:", invoiceError);
          throw invoiceError;
        }
        
        console.log("Invoice saved successfully:", invoiceResponse);
        
        // Insert invoice items using our helper
        const invoiceItems = toDbInvoiceItems(invoiceResponse.id, invoice.items);
        
        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(invoiceItems);
        
        if (itemsError) {
          console.error("Error saving invoice items:", itemsError);
          throw itemsError;
        }
        
        // Update inventory
        for (const item of invoice.items) {
          const { error: updateError } = await supabase
            .rpc('update_product_stock', {
              product_id: item.product_id,
              quantity_change: item.quantity,
              warehouse_id: invoice.warehouse_id
            });
          
          if (updateError) throw updateError;
          
          // Also update purchase price if it's changed
          const product = products.find(p => p.id === item.product_id);
          if (product && product.purchase_price !== item.unit_price) {
            const { error: priceUpdateError } = await supabase
              .from('products')
              .update({ purchase_price: item.unit_price })
              .eq('id', item.product_id);
            
            if (priceUpdateError) throw priceUpdateError;
          }
        }
          // Record payment if paid amount > 0
        if (invoice.paid_amount > 0) {
          // Use our helper function to create payment transaction
          const paymentTransaction = toDbPaymentTransaction(
            invoiceResponse.id,
            invoice.paid_amount,
            invoice.payment_method,
            'payment' // For purchase invoices, we always use 'payment'
          );
          
          const { error: paymentError } = await supabase
            .from('payment_transactions')
            .insert([paymentTransaction]);
          
          if (paymentError) {
            console.error("Error recording payment:", paymentError);
            throw paymentError;
          }
        }
        
        toast({
          title: "تم الحفظ",
          description: "تم إنشاء فاتورة الشراء بنجاح",
        });
        
        // Navigate to invoice view
        if (onSaved) {
          onSaved();
        } else {
          navigate(`/invoices/purchases/${invoiceResponse.id}`);
        }      } else {
        // Update existing invoice
        const { error: invoiceError } = await supabase
          .from('invoices')
          .update(invoiceData)
          .eq('id', invoiceId);
        
        if (invoiceError) throw invoiceError;
        
        // Update purchase invoice items...
        // (Similar logic as for creating a new invoice, with necessary adjustments)
        
        toast({
          title: "تم الحفظ",
          description: "تم تحديث فاتورة الشراء بنجاح",
        });
        
        if (onSaved) {
          onSaved();
        } else {
          navigate(`/invoices/purchases/${invoiceId}`);
        }
      }
    } catch (error) {
      console.error('Error saving invoice:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ الفاتورة",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const saveAsDraft = () => {
    saveInvoice(InvoiceStatus.DRAFT);
  };
  
  const completeInvoice = () => {
    saveInvoice(InvoiceStatus.COMPLETED);
  };
  
  if (isLoading) {
    return <div className="flex justify-center p-8">جاري التحميل...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Top action bar */}
      <div className="flex flex-wrap md:flex-nowrap gap-4 justify-between">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
          <div>
            <Label htmlFor="invoice-number">رقم الفاتورة</Label>
            <Input
              id="invoice-number"
              value={invoice.invoice_number}
              onChange={(e) => setInvoice({ ...invoice, invoice_number: e.target.value })}
              readOnly={mode === 'edit'}
            />
          </div>
          
          <div>
            <Label htmlFor="reference-number">الرقم المرجعي (من المورد)</Label>
            <Input
              id="reference-number"
              value={invoice.reference_number || ''}
              onChange={(e) => setInvoice({ ...invoice, reference_number: e.target.value })}
            />
          </div>
          
          <div>
            <Label htmlFor="supplier-id">المورد</Label>
            <Select 
              value={invoice.supplier_id} 
              onValueChange={handleSupplierChange}
            >
              <SelectTrigger id="supplier-id">
                <SelectValue placeholder="اختر المورد" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((supplier) => (
                  <SelectItem key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="warehouse-id">المستودع</Label>
            <Select 
              value={invoice.warehouse_id || ''} 
              onValueChange={(value) => setInvoice({ ...invoice, warehouse_id: value })}
            >
              <SelectTrigger id="warehouse-id">
                <SelectValue placeholder="اختر المستودع" />
              </SelectTrigger>
              <SelectContent>
                {warehouses.map((warehouse) => (
                  <SelectItem key={warehouse.id} value={warehouse.id}>
                    {warehouse.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={saveAsDraft} disabled={isSaving}>
            <Save className="w-4 h-4 ml-2" />
            حفظ كمسودة
          </Button>
          <Button onClick={completeInvoice} disabled={isSaving}>
            <PrinterIcon className="w-4 h-4 ml-2" />
            إتمام وطباعة
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Products selection */}
        <div className="lg:col-span-8">
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="بحث عن منتج بالاسم أو الباركود..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      startIcon={<Search className="w-4 h-4 text-gray-500" />}
                    />
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="default">
                        <Plus className="w-4 h-4 ml-2" />
                        إضافة منتج
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>إضافة منتج</DialogTitle>
                      </DialogHeader>
                      <Input
                        placeholder="بحث..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="mb-4"
                      />
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                        {filteredProducts.map((product) => (
                          <Card 
                            key={product.id}
                            className="cursor-pointer hover:shadow-md transition-shadow"
                            onClick={() => {
                              addProductToInvoice(product);
                              setSearchTerm('');
                            }}
                          >
                            <CardContent className="p-4">
                              <div className="font-medium">{product.name}</div>
                              <div className="text-sm text-gray-500">{product.barcode}</div>
                              <div className="mt-2 font-bold text-primary">
                                {formatCurrency(product.purchase_price)} دج
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                        {filteredProducts.length === 0 && (
                          <div className="col-span-3 text-center py-4 text-gray-500">
                            لا توجد منتجات مطابقة للبحث
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* Invoice items table */}
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">#</TableHead>
                        <TableHead>المنتج</TableHead>
                        <TableHead className="text-center">سعر الشراء</TableHead>
                        <TableHead className="text-center">الكمية</TableHead>
                        <TableHead className="text-center">الخصم (%)</TableHead>
                        <TableHead className="text-left">الإجمالي</TableHead>
                        <TableHead className="w-[70px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invoice.items.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="h-24 text-center">
                            لا توجد منتجات في الفاتورة
                          </TableCell>
                        </TableRow>
                      ) : (
                        invoice.items.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell>
                              <div className="font-medium">{item.name}</div>
                              {item.barcode && (
                                <div className="text-xs text-gray-500">{item.barcode}</div>
                              )}
                            </TableCell>
                            <TableCell className="text-center">
                              <Input
                                type="number"
                                value={item.unit_price}
                                onChange={(e) => updateProductPrice(index, Number(e.target.value))}
                                className="w-24 text-center mx-auto"
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Input
                                type="number"
                                value={item.quantity}
                                onChange={(e) => updateProductQuantity(index, Number(e.target.value))}
                                className="w-24 text-center mx-auto"
                              />
                            </TableCell>
                            <TableCell className="text-center">
                              <Input
                                type="number"
                                value={item.discount_percent || 0}
                                onChange={(e) => updateProductDiscount(index, Number(e.target.value))}
                                className="w-24 text-center mx-auto"
                              />
                            </TableCell>
                            <TableCell className="text-left font-medium">
                              {formatCurrency(item.total_price)} دج
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => removeProductFromInvoice(index)}
                              >
                                <Trash2 className="w-4 h-4 text-red-500" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Invoice summary and payment */}
        <div className="lg:col-span-4 space-y-6">
          {/* Invoice Summary */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <h3 className="font-bold flex items-center">
                <Calculator className="w-4 h-4 ml-2" />
                ملخص الفاتورة
              </h3>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>المجموع الفرعي:</span>
                  <span>{formatCurrency(invoice.subtotal)} دج</span>
                </div>

                {/* Discount */}
                <div className="space-y-2">
                  <Label htmlFor="discount-percent">الخصم (%):</Label>
                  <Input
                    id="discount-percent"
                    type="number"
                    value={invoice.discount_percent || 0}
                    onChange={(e) => setInvoice({
                      ...invoice, 
                      discount_percent: Number(e.target.value),
                      discount_amount: (invoice.subtotal * Number(e.target.value)) / 100
                    })}
                    className="max-w-[120px]"
                  />
                  <div className="flex justify-between text-sm">
                    <span>قيمة الخصم:</span>
                    <span className="text-red-500">- {formatCurrency(invoice.discount_amount)} دج</span>
                  </div>
                </div>

                {/* Tax */}
                <div className="space-y-2">
                  <Label htmlFor="tax-percent">الضريبة (%):</Label>                  <Input
                    id="tax-percent"
                    type="number"
                    value={invoice.tax_percent || 0}
                    onChange={(e) => setInvoice({
                      ...invoice, 
                      tax_percent: Number(e.target.value),
                      tax_rate: Number(e.target.value),
                      tax_amount: ((invoice.subtotal - invoice.discount_amount) * Number(e.target.value)) / 100
                    })}
                    className="max-w-[120px]"
                  />
                  <div className="flex justify-between text-sm">
                    <span>قيمة الضريبة:</span>
                    <span>{formatCurrency(invoice.tax_amount)} دج</span>
                  </div>
                </div>

                <div className="pt-2 border-t border-dashed flex justify-between font-bold">
                  <span>الإجمالي:</span>
                  <span>{formatCurrency(invoice.total_amount)} دج</span>
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <h3 className="font-bold flex items-center">
                  <Clock className="w-4 h-4 ml-2" />
                  طريقة الدفع
                </h3>

                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant={paymentDetails.method === PaymentMethod.CASH ? "default" : "outline"} 
                    className="w-full justify-start"
                    onClick={() => handlePaymentMethodChange(PaymentMethod.CASH)}
                  >
                    نقداً
                  </Button>
                  <Button 
                    variant={paymentDetails.method === PaymentMethod.BANK_CARD ? "default" : "outline"} 
                    className="w-full justify-start"
                    onClick={() => handlePaymentMethodChange(PaymentMethod.BANK_CARD)}
                  >
                    بطاقة بنكية
                  </Button>
                  <Button 
                    variant={paymentDetails.method === PaymentMethod.CREDIT ? "default" : "outline"} 
                    className="w-full justify-start"
                    onClick={() => handlePaymentMethodChange(PaymentMethod.CREDIT)}
                  >
                    آجل
                  </Button>
                  <Button 
                    variant={paymentDetails.method === PaymentMethod.MIXED ? "default" : "outline"} 
                    className="w-full justify-start"
                    onClick={() => {
                      handlePaymentMethodChange(PaymentMethod.MIXED);
                      handlePartialPaymentToggle(true);
                    }}
                  >
                    مختلط
                  </Button>
                </div>
                
                {paymentDetails.method !== PaymentMethod.CREDIT && (
                  <div className="space-y-2 pt-2">
                    <div className="flex items-center">
                      <input 
                        type="checkbox" 
                        id="partial-payment"
                        checked={paymentDetails.isPartial}
                        onChange={(e) => handlePartialPaymentToggle(e.target.checked)}
                        className="ml-2"
                      />
                      <Label htmlFor="partial-payment">دفع جزئي</Label>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paid-amount">المبلغ المدفوع:</Label>
                      <Input
                        id="paid-amount"
                        type="number"
                        value={paymentDetails.amount}
                        onChange={(e) => handlePaymentAmountChange(Number(e.target.value))}
                      />
                      
                      <div className="flex justify-between text-sm">
                        <span>المبلغ المتبقي:</span>
                        <span 
                          className={invoice.remaining_amount > 0 ? "text-red-500" : "text-green-500"}
                        >
                          {formatCurrency(invoice.remaining_amount)} دج
                        </span>
                      </div>

                      {invoice.remaining_amount < 0 && (
                        <div className="flex justify-between text-sm">
                          <span>الباقي للمورد:</span>
                          <span className="text-blue-500">
                            {formatCurrency(Math.abs(invoice.remaining_amount))} دج
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="pt-2">
                  <Label htmlFor="notes">ملاحظات:</Label>
                  <Textarea
                    id="notes"
                    value={invoice.notes || ''}
                    onChange={(e) => setInvoice({ ...invoice, notes: e.target.value })}
                    placeholder="أي ملاحظات إضافية..."
                    className="h-20"
                  />
                </div>

                <div className="pt-2">
                  <Label htmlFor="warehouse-note" className="flex items-center text-sm">
                    <Warehouse className="w-4 h-4 ml-1" />
                    ملاحظة المستودع:
                  </Label>
                  <div className="text-sm text-muted-foreground mt-1">
                    سيتم إضافة المنتجات تلقائيًا إلى المستودع المحدد عند إتمام الفاتورة
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick actions */}
          <Card>
            <CardContent className="p-6 flex flex-col gap-2">
              <Button onClick={saveAsDraft} variant="outline" disabled={isSaving} className="w-full justify-start">
                <Save className="w-4 h-4 ml-2" />
                حفظ كمسودة
              </Button>
              <Button onClick={completeInvoice} disabled={isSaving} className="w-full justify-start">
                <PrinterIcon className="w-4 h-4 ml-2" />
                إتمام وطباعة
              </Button>
              <Button variant="destructive" onClick={() => navigate('/invoices')} className="w-full justify-start">
                <XCircle className="w-4 h-4 ml-2" />
                إلغاء
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
