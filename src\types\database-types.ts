import { BaseInvoice, InvoiceItem, PaymentMethod, InvoiceStatus } from './invoice';

export interface DbInvoice {
  id: string;
  invoice_number: string;
  type: string;
  status: InvoiceStatus;
  customer_id?: string;
  supplier_id?: string;
  warehouse_id?: string;
  reference_number?: string;
  return_reason?: string;
  notes?: string;
  subtotal: number;
  discount_amount?: number;
  discount_percent?: number;
  tax_amount?: number;
  tax_percent?: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: PaymentMethod;
  created_at: string;
  updated_at: string;
}

export interface DbInvoiceItem {
  id: string;
  invoice_id: string;
  product_id: string;
  name: string;
  barcode?: string;
  quantity: number;
  unit_price: number;
  discount_amount?: number;
  discount_percent?: number;
  tax_percent?: number;
  tax_amount?: number;
  total_price: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type InvoiceInput = Omit<DbInvoice, 'id' | 'status' | 'created_at' | 'updated_at'>;
export type InvoiceItemInput = Omit<DbInvoiceItem, 'id' | 'created_at' | 'updated_at'>;
