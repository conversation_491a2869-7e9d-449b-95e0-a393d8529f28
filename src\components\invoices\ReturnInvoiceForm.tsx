import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Customer } from "@/types/customer";
import { Supplier } from "@/types/supplier";
import { Warehouse } from "@/types/warehouse";
import { Product } from "@/types/product";
import { 
  InvoiceType, 
  PaymentMethod, 
  InvoiceStatus, 
  PaymentStatus,
  BaseInvoice,
  SalesInvoice,
  PurchaseInvoice,
  InvoiceItem 
} from "@/types/invoice";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON><PERSON>itle,
} from "@/components/ui/dialog";
import { 
  Plus, 
  Trash2, 
  Save
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { toDbInvoice, toDbInvoiceItems, toDbPaymentTransaction } from "@/utils/database-helpers";
import { formatCurrency } from "@/utils/currency";
import { format } from "date-fns";

interface ReturnInvoiceFormProps {
  mode?: 'new' | 'edit';
  invoiceId?: string;
  returnType: 'sales' | 'purchase';
  onSaved?: () => void;
}

export function ReturnInvoiceForm({ invoiceId, mode = 'new', returnType, onSaved }: ReturnInvoiceFormProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const isSalesReturn = returnType === 'sales';

  // State for entity lists
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [originalInvoices, setOriginalInvoices] = useState<BaseInvoice[]>([]);
  const [selectedOriginalInvoice, setSelectedOriginalInvoice] = useState<BaseInvoice | null>(null);
  const [isSelectingInvoice, setIsSelectingInvoice] = useState(false);
  const [isLoadingWarehouses, setIsLoadingWarehouses] = useState(false);

  // Initialize invoice state
  const [invoice, setInvoice] = useState<Partial<BaseInvoice & { return_reason: string; warehouse_id: string }>>({
    id: '',
    invoice_number: '',
    reference_number: '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    notes: '',
    subtotal: 0,
    total_amount: 0,
    paid_amount: 0,
    remaining_amount: 0,
    status: InvoiceStatus.DRAFT,
    payment_status: PaymentStatus.UNPAID,
    payment_method: PaymentMethod.CASH,
    type: isSalesReturn ? InvoiceType.SALES_RETURN : InvoiceType.PURCHASE_RETURN,
    warehouse_id: '',
    return_reason: '',
    items: []
  });

  // Initialize payment details state
  const [paymentDetails, setPaymentDetails] = useState({
    subtotal: 0,
    discount_amount: 0,
    tax_amount: 0,
    total_amount: 0,
    paid_amount: 0,
    remaining_amount: 0
  });
  
  // Load necessary data when component mounts
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        const [customersRes, suppliersRes, warehousesRes] = await Promise.all([
          supabase.from('customers').select('*'),
          supabase.from('suppliers').select('*'),
          supabase.from('warehouses').select('*')
        ]);

        if (customersRes.error) throw customersRes.error;
        if (suppliersRes.error) throw suppliersRes.error;
        if (warehousesRes.error) throw warehousesRes.error;

        setCustomers(customersRes.data || []);
        setSuppliers(suppliersRes.data || []);
        setWarehouses(warehousesRes.data || []);

        // If editing existing invoice, load invoice data
        if (mode === 'edit' && invoiceId) {
          const { data: invoiceData, error: invoiceError } = await supabase
            .from('invoices')
            .select(`
              *,
              invoice_items (*)
            `)
            .eq('id', invoiceId)
            .single();

          if (invoiceError) throw invoiceError;
          if (invoiceData) {
            setInvoice({
              ...invoiceData,
              items: invoiceData.invoice_items
            });
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          variant: "destructive",
          title: "خطأ",
          description: "حدث خطأ أثناء تحميل البيانات"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [mode, invoiceId, toast]);

  // Load warehouses when component mounts
  useEffect(() => {
    const loadWarehouses = async () => {
      setIsLoadingWarehouses(true);
      try {
        const { data, error } = await supabase
          .from('warehouses')
          .select('*')
          .eq('is_active', true)
          .order('name');
          
        if (error) throw error;
        
        setWarehouses(data || []);
      } catch (error) {
        console.error('Error loading warehouses:', error);
        toast({
          variant: "destructive",
          title: "خطأ في تحميل المستودعات",
          description: "حدث خطأ أثناء تحميل قائمة المستودعات"
        });
      } finally {
        setIsLoadingWarehouses(false);
      }
    };

    loadWarehouses();
  }, [toast]);

  const handleSelectOriginalInvoice = async (originalInvoice: BaseInvoice) => {
    setIsSelectingInvoice(false);
    if (isSalesReturn) {
      setInvoice(prev => ({
        ...prev,
        customer_id: (originalInvoice as SalesInvoice).customer_id,
        customer_name: (originalInvoice as SalesInvoice).customer_name,
        returned_from_invoice_id: originalInvoice.id
      }));
    } else {
      setInvoice(prev => ({
        ...prev,
        supplier_id: (originalInvoice as PurchaseInvoice).supplier_id,
        supplier_name: (originalInvoice as PurchaseInvoice).supplier_name,
        returned_from_invoice_id: originalInvoice.id
      }));
    }
    setSelectedOriginalInvoice(originalInvoice);
  };

  const addItemToReturn = (item: InvoiceItem) => {
    if (!invoice.items) return;
    
    if (invoice.items.some(existingItem => existingItem.product_id === item.product_id)) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "هذا المنتج موجود بالفعل في الفاتورة"
      });
      return;
    }

    const newItem: InvoiceItem = {
      ...item,
      quantity: 1,
      total_price: item.unit_price
    };

    setInvoice(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));
  };

  const updateReturnQuantity = useCallback((index: number, quantity: number) => {
    if (!selectedOriginalInvoice?.items || !invoice.items) return;

    const originalItem = selectedOriginalInvoice.items.find(
      (item) => item.product_id === invoice.items[index].product_id
    );

    if (originalItem && quantity > originalItem.quantity) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "كمية الإرجاع لا يمكن أن تتجاوز الكمية الأصلية"
      });
      return;
    }

    const updatedItems = [...invoice.items];
    updatedItems[index].quantity = quantity;
    updatedItems[index].total_price = quantity * updatedItems[index].unit_price;

    if (updatedItems[index].discount_percent && updatedItems[index].discount_percent > 0) {
      updatedItems[index].discount_amount =
        (updatedItems[index].unit_price * quantity) * (updatedItems[index].discount_percent / 100);
      updatedItems[index].total_price =
        (updatedItems[index].unit_price * quantity) - (updatedItems[index].discount_amount || 0);
    }

    setInvoice(prev => ({
      ...prev,
      items: updatedItems
    }));
  }, [invoice.items, selectedOriginalInvoice?.items, toast]);

  useEffect(() => {
    const calculateTotals = () => {
      if (!invoice.items) return;

      const subtotal = invoice.items.reduce((sum, item) => sum + (item.total_price || 0), 0);
      const discountAmount = invoice.discount_percent ? 
        subtotal * (invoice.discount_percent / 100) : 
        (invoice.discount_amount || 0);
      const afterDiscount = subtotal - discountAmount;
      const taxAmount = invoice.tax_percent ? 
        afterDiscount * (invoice.tax_percent / 100) : 
        0;
      const totalAmount = afterDiscount + taxAmount;
      const remainingAmount = totalAmount - (invoice.paid_amount || 0);

      setInvoice(prev => ({
        ...prev,
        subtotal,
        discount_amount: discountAmount,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        remaining_amount: remainingAmount
      }));

      setPaymentDetails(prev => ({
        ...prev,
        subtotal,
        discount_amount: discountAmount,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        remaining_amount: remainingAmount
      }));
    };

    calculateTotals();
  }, [
    invoice.items, 
    invoice.discount_percent, 
    invoice.tax_percent, 
    invoice.discount_amount, 
    invoice.paid_amount
  ]);

  const updatePaymentAmount = (amount: number) => {
    const newRemainingAmount = (invoice.total_amount || 0) - amount;
    
    setPaymentDetails(prev => ({
      ...prev,
      paid_amount: amount,
      remaining_amount: newRemainingAmount
    }));

    setInvoice(prev => ({
      ...prev,
      paid_amount: amount,
      remaining_amount: newRemainingAmount,
      payment_status: amount === 0 ? PaymentStatus.UNPAID :
        amount === prev.total_amount ? PaymentStatus.PAID :
        PaymentStatus.PARTIAL
    }));
  };

  // Handle customer/supplier selection
  const handleCustomerChange = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    setInvoice(prev => ({
      ...prev,
      customer_id: customerId,
      customer_name: customer?.name
    }));
  };

  const handleSupplierChange = (supplierId: string) => {
    const supplier = suppliers.find(s => s.id === supplierId);
    setInvoice(prev => ({
      ...prev,
      supplier_id: supplierId,
      supplier_name: supplier?.name
    }));
  };

  const handleWarehouseChange = (warehouseId: string) => {
    setInvoice(prev => ({
      ...prev,
      warehouse_id: warehouseId
    }));
  };

  const handleSave = async (status: InvoiceStatus) => {
    // Type guard for required fields
    if (isSalesReturn && !invoice.customer_id) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يجب تحديد العميل"
      });
      return;
    }

    if (!isSalesReturn && !invoice.supplier_id) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يجب تحديد المورد"
      });
      return;
    }

    if (!invoice.items || invoice.items.length === 0) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يجب إضافة منتج واحد على الأقل"
      });
      return;
    }

    if (!invoice.warehouse_id) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يجب تحديد المستودع"
      });
      return;
    }

    if (!invoice.return_reason) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يجب تحديد سبب الإرجاع"
      });
      return;
    }

    setIsSaving(true);

    try {
      const invoiceData = toDbInvoice(invoice, status);

      if (mode === 'new') {
        const { data: invoiceResponse, error: invoiceError } = await supabase
          .from('invoices')
          .insert(invoiceData)
          .select()
          .single();

        if (invoiceError) throw invoiceError;

        // Insert return items
        const returnItems = toDbInvoiceItems(invoiceResponse.id, invoice.items);
        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(returnItems);

        if (itemsError) throw itemsError;

        // Update inventory
        for (const item of invoice.items) {
          const quantityChange = isSalesReturn ? item.quantity : -item.quantity;
          const { error: stockError } = await supabase.rpc('update_stock', {
              p_product_id: item.product_id,
              p_quantity: quantityChange,
              warehouse_id: invoice.warehouse_id
          });

          if (stockError) throw stockError;
        }

        // Create payment transaction if paid amount > 0
        if (invoice.paid_amount && invoice.paid_amount > 0 && invoice.payment_method) {
          const { error: paymentError } = await supabase
            .from('payment_transactions')
            .insert(toDbPaymentTransaction(
              invoiceResponse.id,
              invoice.paid_amount,
              invoice.payment_method,
              isSalesReturn ? 'refund' : 'payment'
            ));

          if (paymentError) throw paymentError;
        }

        // Update original invoice status if this is a return
        if (invoice.returned_from_invoice_id) {
          const { error: updateError } = await supabase
            .from('invoices')
            .update({ status: InvoiceStatus.RETURNED })
            .eq('id', invoice.returned_from_invoice_id);

          if (updateError) throw updateError;
        }

        toast({
          variant: "default",
          title: "نجاح",
          description: `تم إنشاء فاتورة ${isSalesReturn ? 'إرجاع مبيعات' : 'إرجاع مشتريات'} بنجاح`,
        });

        if (onSaved) {
          onSaved();
        } else {
          navigate(`/invoices/${isSalesReturn ? 'sales-returns' : 'purchase-returns'}/${invoiceResponse.id}`);
        }

      } else {
        // Update existing invoice
        const { error: updateError } = await supabase
          .from('invoices')
          .update(invoiceData)
          .eq('id', invoiceId);

        if (updateError) throw updateError;

        toast({
          variant: "default",
          title: "نجاح",
          description: `تم تحديث فاتورة ${isSalesReturn ? 'إرجاع المبيعات' : 'إرجاع المشتريات'} بنجاح`,
        });

        if (onSaved) {
          onSaved();
        } else {
          navigate(`/invoices/${isSalesReturn ? 'sales-returns' : 'purchase-returns'}/${invoiceId}`);
        }
      }
    } catch (error) {
      console.error('Error saving return invoice:', error);
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ الفاتورة. يرجى المحاولة مرة أخرى."
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="loading-spinner" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Form content components */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid gap-4 md:grid-cols-3">
            {/* Warehouse Selection */}
            <div className="space-y-2">
              <Label htmlFor="warehouse">المستودع</Label>
              <Select
                value={invoice.warehouse_id}
                onValueChange={handleWarehouseChange}
                disabled={isLoadingWarehouses}
              >
                <SelectTrigger id="warehouse" className="w-full">
                  <SelectValue placeholder="اختر المستودع" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.map((warehouse) => (
                    <SelectItem key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Return Reason */}
            <div className="space-y-2">
              <Label htmlFor="return_reason">سبب الإرجاع</Label>
              <Textarea
                id="return_reason"
                value={invoice.return_reason}
                onChange={(e) => setInvoice(prev => ({ ...prev, return_reason: e.target.value }))}
                placeholder="اكتب سبب الإرجاع هنا..."
                className="h-20"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
