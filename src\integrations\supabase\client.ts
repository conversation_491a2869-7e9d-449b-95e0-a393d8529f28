// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://kznmwnuqyikjnfdzcptr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt6bm13bnVxeWlram5mZHpjcHRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDY2ODUsImV4cCI6MjA2NDM4MjY4NX0.eV1t38c026adapeG6JS3AXzYa6faaMICQjuhynYWb3A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);