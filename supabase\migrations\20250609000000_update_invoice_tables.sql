-- Update invoice tables structure

-- Create enum types
CREATE TYPE invoice_status AS ENUM ('draft', 'completed', 'canceled', 'returned');
CREATE TYPE payment_status AS ENUM ('paid', 'partial', 'unpaid');
CREATE TYPE payment_method AS ENUM ('cash', 'bank_card', 'credit', 'mixed');
CREATE TYPE invoice_type AS ENUM ('sales', 'purchase', 'sales_return', 'purchase_return');

-- Create or update invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number VARCHAR(255) NOT NULL UNIQUE,
    invoice_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMP WITH TIME ZONE,
    customer_id UUID REFERENCES customers(id),
    supplier_id UUID REFERENCES suppliers(id),
    warehouse_id UUID REFERENCES warehouses(id),
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    remaining_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    status invoice_status NOT NULL DEFAULT 'draft',
    payment_status payment_status NOT NULL DEFAULT 'unpaid',
    payment_method payment_method NOT NULL DEFAULT 'cash',
    notes TEXT,
    type invoice_type NOT NULL,
    returned_from_invoice_id UUID REFERENCES invoices(id),
    return_reason TEXT,
    CONSTRAINT check_customer_or_supplier CHECK (
        (type IN ('sales', 'sales_return') AND customer_id IS NOT NULL AND supplier_id IS NULL) OR
        (type IN ('purchase', 'purchase_return') AND supplier_id IS NOT NULL AND customer_id IS NULL)
    )
);

-- Create index on invoice_number
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_type ON invoices(type);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON invoices(created_at);

-- Create invoice_items table
CREATE TABLE IF NOT EXISTS invoice_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    name VARCHAR(255) NOT NULL,
    barcode VARCHAR(255),
    quantity DECIMAL(10,2) NOT NULL DEFAULT 0,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    tax_percent DECIMAL(5,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on invoice_items
CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_product_id ON invoice_items(product_id);

-- Create payment_transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    payment_method payment_method NOT NULL DEFAULT 'cash',
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    transaction_type VARCHAR(50), -- 'payment' or 'refund'
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on payment_transactions
CREATE INDEX IF NOT EXISTS idx_payment_transactions_invoice_id ON payment_transactions(invoice_id);

-- Create function to update invoice totals
CREATE OR REPLACE FUNCTION update_invoice_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Update invoice totals
    WITH item_totals AS (
        SELECT 
            invoice_id,
            SUM(total_price) as total,
            SUM(discount_amount) as discount,
            SUM(tax_amount) as tax
        FROM invoice_items
        WHERE invoice_id = NEW.invoice_id
        GROUP BY invoice_id
    )
    UPDATE invoices i
    SET 
        subtotal = t.total,
        total_amount = t.total - COALESCE(i.discount_amount, 0) + COALESCE(i.tax_amount, 0),
        updated_at = CURRENT_TIMESTAMP
    FROM item_totals t
    WHERE i.id = t.invoice_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for invoice_items
CREATE TRIGGER update_invoice_totals_trigger
AFTER INSERT OR UPDATE OR DELETE ON invoice_items
FOR EACH ROW EXECUTE FUNCTION update_invoice_totals();

-- Create function to update payment status
CREATE OR REPLACE FUNCTION update_payment_status()
RETURNS TRIGGER AS $$
BEGIN
    WITH payment_totals AS (
        SELECT 
            invoice_id,
            SUM(amount) as total_paid
        FROM payment_transactions
        WHERE invoice_id = NEW.invoice_id
        GROUP BY invoice_id
    )
    UPDATE invoices i
    SET 
        paid_amount = COALESCE(p.total_paid, 0),
        remaining_amount = i.total_amount - COALESCE(p.total_paid, 0),
        payment_status = 
            CASE 
                WHEN COALESCE(p.total_paid, 0) = 0 THEN 'unpaid'
                WHEN COALESCE(p.total_paid, 0) >= i.total_amount THEN 'paid'
                ELSE 'partial'
            END,
        updated_at = CURRENT_TIMESTAMP
    FROM payment_totals p
    WHERE i.id = p.invoice_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for payment_transactions
CREATE TRIGGER update_payment_status_trigger
AFTER INSERT OR UPDATE OR DELETE ON payment_transactions
FOR EACH ROW EXECUTE FUNCTION update_payment_status();

-- Create function to update stock
CREATE OR REPLACE FUNCTION update_product_stock(
    product_id UUID,
    quantity_change DECIMAL,
    warehouse_id UUID
)
RETURNS VOID AS $$
BEGIN
    -- Update stock in warehouse_products
    INSERT INTO warehouse_products (warehouse_id, product_id, quantity)
    VALUES (warehouse_id, product_id, quantity_change)
    ON CONFLICT (warehouse_id, product_id)
    DO UPDATE SET quantity = warehouse_products.quantity + quantity_change;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;

-- Create policies for invoices
CREATE POLICY "Allow full access to authenticated users" ON invoices
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policies for invoice_items
CREATE POLICY "Allow full access to authenticated users" ON invoice_items
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policies for payment_transactions
CREATE POLICY "Allow full access to authenticated users" ON payment_transactions
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);
