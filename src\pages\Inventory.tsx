
import { Layout } from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Edit, 
  Trash2,
  MoreVertical,
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Warehouse,
  BarChart3
} from "lucide-react";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Di<PERSON>, DialogContent, Dialog<PERSON>eader, Di<PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

interface Product {
  id: string;
  name: string;
  category: string | null;
  barcode: string | null;
  stock_quantity: number;
  min_stock_level: number | null;
  selling_price: number;
  purchase_price: number;
  unit: string;
  brand: string | null;
}

const getStatusBadge = (quantity: number, minStock: number) => {
  if (quantity === 0) {
    return <Badge variant="destructive" className="bg-red-100 text-red-800">نفد المخزون</Badge>;
  } else if (quantity <= minStock) {
    return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">مخزون منخفض</Badge>;
  } else {
    return <Badge variant="default" className="bg-green-100 text-green-800">متوفر</Badge>;
  }
};

const Inventory = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [isStockDialogOpen, setIsStockDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [stockQuantity, setStockQuantity] = useState(0);
  const { toast } = useToast();

  // جلب المنتجات من قاعدة البيانات
  const { data: inventory = [], isLoading, refetch } = useQuery({
    queryKey: ['inventory'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Product[];
    }
  });

  const filteredInventory = inventory.filter(item => {
    const matchesCategory = filterCategory === "all" || item.category === filterCategory;
    const matchesSearch = item.name.includes(searchTerm) || (item.barcode && item.barcode.includes(searchTerm));
    
    let matchesStatus = true;
    if (filterStatus === "low_stock") {
      matchesStatus = item.stock_quantity <= (item.min_stock_level || 10) && item.stock_quantity > 0;
    } else if (filterStatus === "out_of_stock") {
      matchesStatus = item.stock_quantity === 0;
    } else if (filterStatus === "in_stock") {
      matchesStatus = item.stock_quantity > (item.min_stock_level || 10);
    }
    
    return matchesCategory && matchesSearch && matchesStatus;
  });

  const totalValue = inventory.reduce((sum, item) => sum + (item.stock_quantity * item.purchase_price), 0);
  const totalItems = inventory.length;
  const lowStockItems = inventory.filter(item => item.stock_quantity <= (item.min_stock_level || 10) && item.stock_quantity > 0).length;
  const outOfStockItems = inventory.filter(item => item.stock_quantity === 0).length;

  const categories = [...new Set(inventory.map(item => item.category).filter(Boolean))];

  const handleUpdateStock = async () => {
    if (!selectedProduct) return;

    try {
      const { error } = await supabase
        .from('products')
        .update({ stock_quantity: stockQuantity })
        .eq('id', selectedProduct.id);

      if (error) throw error;

      toast({
        title: "تم التحديث",
        description: "تم تحديث كمية المخزون بنجاح",
      });

      setIsStockDialogOpen(false);
      setSelectedProduct(null);
      setStockQuantity(0);
      refetch();
    } catch (error) {
      console.error('Error updating stock:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحديث المخزون",
        variant: "destructive",
      });
    }
  };

  const openStockDialog = (product: Product) => {
    setSelectedProduct(product);
    setStockQuantity(product.stock_quantity);
    setIsStockDialogOpen(true);
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">جاري التحميل...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة المخزون</h1>
            <p className="text-gray-600 mt-1">تتبع وإدارة مخزون المنتجات</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <Download className="w-4 h-4" />
              تصدير التقرير
            </Button>
            <Button className="gap-2" onClick={() => window.location.href = '/products'}>
              <Plus className="w-4 h-4" />
              إضافة منتج
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold text-blue-900">{totalItems}</p>
                </div>
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Package className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium">قيمة المخزون</p>
                  <p className="text-2xl font-bold text-green-900">{totalValue.toLocaleString()} ريال</p>
                </div>
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-600 text-sm font-medium">مخزون منخفض</p>
                  <p className="text-2xl font-bold text-yellow-900">{lowStockItems}</p>
                </div>
                <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                  <TrendingDown className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-600 text-sm font-medium">نفد المخزون</p>
                  <p className="text-2xl font-bold text-red-900">{outOfStockItems}</p>
                </div>
                <div className="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Alerts */}
        {(lowStockItems > 0 || outOfStockItems > 0) && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-yellow-800 font-medium">تنبيهات المخزون</p>
                  <p className="text-yellow-700 text-sm">
                    {outOfStockItems > 0 && `${outOfStockItems} منتج نفد من المخزون`}
                    {outOfStockItems > 0 && lowStockItems > 0 && " و "}
                    {lowStockItems > 0 && `${lowStockItems} منتج مخزونه منخفض`}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex gap-4 items-center w-full md:w-auto">
                <div className="relative flex-1 md:w-80">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في المنتجات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Filter className="w-4 h-4" />
                      الفئة
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setFilterCategory("all")}>
                      جميع الفئات
                    </DropdownMenuItem>
                    {categories.map(category => (
                      <DropdownMenuItem key={category} onClick={() => setFilterCategory(category!)}>
                        {category}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Filter className="w-4 h-4" />
                      الحالة
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                      جميع الحالات
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilterStatus("in_stock")}>
                      متوفر
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilterStatus("low_stock")}>
                      مخزون منخفض
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setFilterStatus("out_of_stock")}>
                      نفد المخزون
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Inventory Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المخزون</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">اسم المنتج</TableHead>
                  <TableHead className="text-right">الفئة</TableHead>
                  <TableHead className="text-right">الكمية</TableHead>
                  <TableHead className="text-right">الحد الأدنى</TableHead>
                  <TableHead className="text-right">سعر الشراء</TableHead>
                  <TableHead className="text-right">سعر البيع</TableHead>
                  <TableHead className="text-right">القيمة الإجمالية</TableHead>
                  <TableHead className="text-right">الحالة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInventory.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.name}</div>
                        {item.brand && (
                          <div className="text-xs text-gray-500">{item.brand}</div>
                        )}
                        {item.barcode && (
                          <div className="text-xs text-gray-400 font-mono">{item.barcode}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{item.category || "غير محدد"}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={`font-semibold ${
                          item.stock_quantity === 0 ? 'text-red-600' : 
                          item.stock_quantity <= (item.min_stock_level || 10) ? 'text-yellow-600' : 'text-green-600'
                        }`}>
                          {item.stock_quantity} {item.unit}
                        </span>
                        {item.stock_quantity <= (item.min_stock_level || 10) && item.stock_quantity > 0 && (
                          <AlertTriangle className="w-4 h-4 text-yellow-500" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{item.min_stock_level || 10}</TableCell>
                    <TableCell>{item.purchase_price.toLocaleString()} ريال</TableCell>
                    <TableCell className="font-semibold text-primary">{item.selling_price.toLocaleString()} ريال</TableCell>
                    <TableCell className="font-semibold">{(item.stock_quantity * item.purchase_price).toLocaleString()} ريال</TableCell>
                    <TableCell>{getStatusBadge(item.stock_quantity, item.min_stock_level || 10)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem className="gap-2" onClick={() => openStockDialog(item)}>
                            <Edit className="w-4 h-4" />
                            تعديل الكمية
                          </DropdownMenuItem>
                          <DropdownMenuItem className="gap-2">
                            <TrendingUp className="w-4 h-4" />
                            إضافة مخزون
                          </DropdownMenuItem>
                          <DropdownMenuItem className="gap-2">
                            <Warehouse className="w-4 h-4" />
                            نقل مخزون
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {filteredInventory.length === 0 && (
              <div className="text-center py-8">
                <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد منتجات</h3>
                <p className="text-gray-500">
                  {searchTerm || filterCategory !== "all" || filterStatus !== "all" ? "لم يتم العثور على منتجات مطابقة للبحث" : "ابدأ بإضافة أول منتج لك"}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Stock Update Dialog */}
        <Dialog open={isStockDialogOpen} onOpenChange={setIsStockDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تحديث كمية المخزون</DialogTitle>
            </DialogHeader>
            {selectedProduct && (
              <div className="space-y-4">
                <div>
                  <Label>المنتج</Label>
                  <p className="text-lg font-medium">{selectedProduct.name}</p>
                </div>
                <div>
                  <Label htmlFor="stock_quantity">الكمية الجديدة</Label>
                  <Input
                    id="stock_quantity"
                    type="number"
                    min="0"
                    value={stockQuantity}
                    onChange={(e) => setStockQuantity(parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleUpdateStock}>تحديث</Button>
                  <Button variant="outline" onClick={() => setIsStockDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default Inventory;
