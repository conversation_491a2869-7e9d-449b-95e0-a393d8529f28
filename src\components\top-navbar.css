/* Top Navbar Styles */
:root {
  --primary-color: #3b82f6;
  --primary-color-light: #60a5fa;
  --primary-color-rgb: 59, 130, 246;
}

.top-navbar {
  display: flex;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 0.75rem 1.5rem;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 50;
  direction: rtl;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-left: 2rem;
}

.app-logo {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.25);
}

.brand-text {
  font-size: 1.25rem;
  font-weight: bold;
  background: linear-gradient(to left, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.navbar-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.navbar-links::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.navbar-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  color: #64748b;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  background: transparent;
  border: none;
  cursor: pointer;
}

.navbar-item:hover {
  background-color: #f1f5f9;
  transform: translateY(-1px);
  color: #334155;
}

.navbar-item-active {
  background: linear-gradient(120deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  color: white;
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.2);
}

.navbar-item-active:hover {
  background: linear-gradient(120deg, var(--primary-color) 0%, var(--primary-color-light) 100%);
  color: white;
}

.navbar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .navbar-item span {
    display: none;
  }

  .navbar-brand .brand-text {
    display: none;
  }

  .navbar-brand {
    margin-left: 0.5rem;
  }

  .navbar-links {
    justify-content: center;
  }
  
  .user-section {
    margin-left: 0.5rem;
  }
}

/* Mobile menu styles */
.mobile-navbar-item {
  transition: all 0.2s ease;
}

.mobile-navbar-item:hover {
  background-color: #f1f5f9;
}
