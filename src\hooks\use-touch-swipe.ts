import { useState, useEffect } from 'react';

interface SwipeInput {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  minSwipeDistance?: number;
  element?: HTMLElement | null;
}

export default function useSwipe({
  onSwipeLeft,
  onSwipeRight,
  minSwipeDistance = 100,
  element = null
}: SwipeInput = {}) {
  // مركز اللمس الابتدائي
  const [touchStart, setTouchStart] = useState<number | null>(null);
  // مركز اللمس النهائي
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  
  // رصد بداية اللمس وتخزين الإحداثيات X
  const handleTouchStart = (e: TouchEvent) => {
    setTouchEnd(null); // إعادة ضبط النهاية
    setTouchStart(e.touches[0].clientX);
  };
  
  // رصد تحرك اللمس وتحديث الإحداثيات X النهائية
  const handleTouchMove = (e: TouchEvent) => {
    setTouchEnd(e.touches[0].clientX);
  };
  
  // رصد نهاية اللمس وتنفيذ الإجراء المطلوب إذا كانت المسافة كافية
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;
    
    if (isLeftSwipe && onSwipeLeft) onSwipeLeft();
    if (isRightSwipe && onSwipeRight) onSwipeRight();
    
    // إعادة ضبط القيم
    setTouchEnd(null);
    setTouchStart(null);
  };
  
  useEffect(() => {
    // إذا تم تحديد عنصر محدد، أضف مستمعات الأحداث إليه
    const target = element || document;
    
    const touchStartHandler = (e: Event) => handleTouchStart(e as TouchEvent);
    const touchMoveHandler = (e: Event) => handleTouchMove(e as TouchEvent);
    
    if (target) {
      target.addEventListener('touchstart', touchStartHandler);
      target.addEventListener('touchmove', touchMoveHandler);
      target.addEventListener('touchend', handleTouchEnd);
    }
    
    // تنظيف عند التفكيك
    return () => {
      if (target) {
        target.removeEventListener('touchstart', touchStartHandler);
        target.removeEventListener('touchmove', touchMoveHandler);
        target.removeEventListener('touchend', handleTouchEnd);
      }
    };
  }, [element, onSwipeLeft, onSwipeRight, minSwipeDistance]); // إعادة التنفيذ عند تغير أي من هذه القيم
  
  // إرجاع قيم اللمس الحالية (قد تكون مفيدة لغرض التصميم)
  return {
    touchStart,
    touchEnd,
    swipeDistance: touchStart && touchEnd ? touchStart - touchEnd : 0
  };
}
