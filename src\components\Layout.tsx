
import React from 'react';
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from './AppSidebar';
import { Header } from './Header';
// import './layout-fix.css'; // استيراد التنسيق المخصص (تم التعليق عليه لأنه غير موجود)

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <SidebarProvider>
      <div className="w-screen bg-gray-50 relative min-h-screen max-w-[100vw] overflow-x-hidden" dir="rtl">
        <AppSidebar />
        <div className="main-content-with-sidebar">
          <Header />
          <main className="p-6 flex-grow relative z-10 w-full overflow-x-hidden">
            <div className="w-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
