import { InvoiceItem, InvoiceStatus, InvoiceType, PaymentMethod, PaymentStatus } from '@/types/invoice';

interface DbInvoice {
  id?: string;
  invoice_number: string;
  type: string; // تم تغييره إلى string للتوافق مع قاعدة البيانات
  status: string; // تم تغييره إلى string للتوافق مع قاعدة البيانات
  customer_id?: string;
  supplier_id?: string;
  warehouse_id?: string;
  reference_number?: string;
  return_reason?: string;
  notes?: string;
  due_date?: string;
  subtotal: number;
  discount_amount?: number;
  discount_percent?: number;
  tax_amount?: number;
  tax_rate?: number; // تم تغييره من tax_percent لمطابقة اسم الحقل في قاعدة البيانات
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: string;
  payment_method: string;
  created_at: string;
  updated_at: string;
}

type InvoiceInput = Omit<DbInvoice, 'status' | 'created_at' | 'updated_at'>;

export function toDbInvoice(invoice: InvoiceInput, status: InvoiceStatus | string): DbInvoice {
  // Create a copy of the invoice and explicitly pick only the fields we want to send
  const { 
    invoice_number,
    type,
    customer_id,
    supplier_id,
    warehouse_id,
    reference_number,
    return_reason,
    notes,
    subtotal,
    discount_amount,
    discount_percent,
    tax_amount,
    tax_rate, // استخدام الاسم المطابق لاسم الحقل في قاعدة البيانات
    total_amount,
    paid_amount,
    remaining_amount,
    payment_status,
    payment_method,
    due_date,
  } = invoice;
  
  // التأكد من أن نوع الفاتورة والحالة يتوافقان مع قاعدة البيانات
  // تحويل Enum إلى قيمة string للتوافق مع قاعدة البيانات
  const dbInvoice: DbInvoice = {
    invoice_number,
    type: String(type),
    status: String(status),
    customer_id: customer_id || null,
    supplier_id: supplier_id || null,
    warehouse_id: warehouse_id || null,
    reference_number: reference_number || null,
    return_reason: return_reason || null,
    notes: notes || null,
    due_date: due_date || null,
    subtotal,
    discount_amount: discount_amount || 0,
    discount_percent: discount_percent || 0,
    tax_amount: tax_amount || 0,
    tax_rate: tax_rate || 0,
    total_amount,
    paid_amount,
    remaining_amount,
    payment_status: String(payment_status),
    payment_method: String(payment_method),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // Log what we're sending to help debug
  console.log("Database invoice data being sent:", dbInvoice);
  
  return dbInvoice;
}

export function toDbInvoiceItems(invoiceId: string, items: InvoiceItem[]) {
  return items.map(item => ({
    ...item,
    invoice_id: invoiceId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));
}

export function toDbPaymentTransaction(
  invoice_id: string,
  amount: number, 
  method: string,
  transaction_type: 'payment' | 'refund'
) {
  return {
    invoice_id,
    amount,
    method,
    transaction_type,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}
