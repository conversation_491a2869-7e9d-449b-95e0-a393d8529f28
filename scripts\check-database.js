// filepath: c:\Users\<USER>\Desktop\dz\dz-store-manager-web\supabase\scripts\check-database.js
import { createClient } from '@supabase/supabase-js';

// Replace these values with your actual Supabase URL and key
const SUPABASE_URL = "https://kznmwnuqyikjnfdzcptr.supabase.co";
const SUPABASE_SERVICE_KEY = "your-service-key"; // Replace with actual service key

async function main() {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  console.log("Checking database schema for invoices table...");
  
  // Query the database schema information
  const { data: columns, error } = await supabase
    .from('pg_catalog.information_schema.columns')
    .select('column_name, data_type')
    .eq('table_name', 'invoices');
  
  if (error) {
    console.error("Error checking schema:", error);
    return;
  }
  
  console.log("Columns in invoices table:", columns);
  
  // Check if discount_percent column exists
  const hasDiscountPercent = columns.some(col => col.column_name === 'discount_percent');
  const hasTaxRate = columns.some(col => col.column_name === 'tax_rate');
  
  console.log(`Column 'discount_percent' exists: ${hasDiscountPercent}`);
  console.log(`Column 'tax_rate' exists: ${hasTaxRate}`);
  
  if (!hasDiscountPercent || !hasTaxRate) {
    console.log("Creating missing columns in the invoices table...");
    
    // SQL to add missing columns
    const addColumnsSQL = `
      ALTER TABLE invoices 
        ${!hasDiscountPercent ? "ADD COLUMN discount_percent DECIMAL(5,2) DEFAULT 0," : ""}
        ${!hasTaxRate ? "ADD COLUMN tax_rate DECIMAL(5,2) DEFAULT 0" : ""};
      
      SELECT pg_notify('pgrst', 'reload schema');
    `;
    
    const { error: alterError } = await supabase.rpc('exec_sql', { sql: addColumnsSQL });
    
    if (alterError) {
      console.error("Error adding columns:", alterError);
    } else {
      console.log("Missing columns added successfully!");
    }
  }
}

main().catch(console.error);
