
import { Layout } from "@/components/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Settings as SettingsIcon, 
  User, 
  Store, 
  Bell, 
  Shield, 
  Palette,
  Database,
  Globe,
  Save,
  Upload,
  Download,
  Trash2,
  Key,
  Mail,
  Phone,
  MapPin
} from "lucide-react";
import { useState } from "react";

const Settings = () => {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    lowStock: true,
    newOrders: true,
    payments: true
  });

  const [storeSettings, setStoreSettings] = useState({
    storeName: "متجر الإلكترونيات الحديثة",
    storeDescription: "متجر متخصص في بيع الإلكترونيات والأجهزة الذكية",
    email: "<EMAIL>",
    phone: "+966501234567",
    address: "الرياض، المملكة العربية السعودية",
    currency: "SAR",
    language: "ar",
    taxRate: "15"
  });

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
            <SettingsIcon className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">الإعدادات</h1>
            <p className="text-gray-600">إدارة إعدادات النظام والحساب</p>
          </div>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6">
            <TabsTrigger value="profile" className="gap-2">
              <User className="w-4 h-4" />
              الملف الشخصي
            </TabsTrigger>
            <TabsTrigger value="store" className="gap-2">
              <Store className="w-4 h-4" />
              إعدادات المتجر
            </TabsTrigger>
            <TabsTrigger value="notifications" className="gap-2">
              <Bell className="w-4 h-4" />
              الإشعارات
            </TabsTrigger>
            <TabsTrigger value="security" className="gap-2">
              <Shield className="w-4 h-4" />
              الأمان
            </TabsTrigger>
            <TabsTrigger value="appearance" className="gap-2">
              <Palette className="w-4 h-4" />
              المظهر
            </TabsTrigger>
            <TabsTrigger value="system" className="gap-2">
              <Database className="w-4 h-4" />
              النظام
            </TabsTrigger>
          </TabsList>

          {/* Profile Settings */}
          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  معلومات الملف الشخصي
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center gap-6">
                  <Avatar className="w-20 h-20">
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback className="bg-primary text-white text-lg font-bold">أد</AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <Button variant="outline" className="gap-2">
                      <Upload className="w-4 h-4" />
                      تغيير الصورة
                    </Button>
                    <p className="text-sm text-gray-500">JPG, PNG أو GIF. الحد الأقصى 5MB</p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">الاسم الأول</Label>
                    <Input id="firstName" defaultValue="أحمد" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">اسم العائلة</Label>
                    <Input id="lastName" defaultValue="محمد" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input id="phone" defaultValue="+966501234567" />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="bio">نبذة شخصية</Label>
                    <Input id="bio" defaultValue="مدير متجر الإلكترونيات" />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button className="gap-2">
                    <Save className="w-4 h-4" />
                    حفظ التغييرات
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Store Settings */}
          <TabsContent value="store" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Store className="w-5 h-5" />
                  إعدادات المتجر
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="storeName">اسم المتجر</Label>
                    <Input 
                      id="storeName" 
                      value={storeSettings.storeName}
                      onChange={(e) => setStoreSettings({...storeSettings, storeName: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="storeEmail">البريد الإلكتروني</Label>
                    <Input 
                      id="storeEmail" 
                      type="email"
                      value={storeSettings.email}
                      onChange={(e) => setStoreSettings({...storeSettings, email: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="storePhone">رقم الهاتف</Label>
                    <Input 
                      id="storePhone"
                      value={storeSettings.phone}
                      onChange={(e) => setStoreSettings({...storeSettings, phone: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">العملة</Label>
                    <Select value={storeSettings.currency} onValueChange={(value) => setStoreSettings({...storeSettings, currency: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SAR">ريال سعودي (SAR)</SelectItem>
                        <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                        <SelectItem value="EUR">يورو (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="language">اللغة</Label>
                    <Select value={storeSettings.language} onValueChange={(value) => setStoreSettings({...storeSettings, language: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ar">العربية</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="taxRate">نسبة الضريبة (%)</Label>
                    <Input 
                      id="taxRate"
                      type="number"
                      value={storeSettings.taxRate}
                      onChange={(e) => setStoreSettings({...storeSettings, taxRate: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="storeAddress">عنوان المتجر</Label>
                    <Input 
                      id="storeAddress"
                      value={storeSettings.address}
                      onChange={(e) => setStoreSettings({...storeSettings, address: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="storeDescription">وصف المتجر</Label>
                    <Input 
                      id="storeDescription"
                      value={storeSettings.storeDescription}
                      onChange={(e) => setStoreSettings({...storeSettings, storeDescription: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button className="gap-2">
                    <Save className="w-4 h-4" />
                    حفظ الإعدادات
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notifications */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  إعدادات الإشعارات
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>إشعارات البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-500">تلقي إشعارات عبر البريد الإلكتروني</p>
                    </div>
                    <Switch 
                      checked={notifications.email}
                      onCheckedChange={(checked) => setNotifications({...notifications, email: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>إشعارات الرسائل النصية</Label>
                      <p className="text-sm text-gray-500">تلقي إشعارات عبر الرسائل النصية</p>
                    </div>
                    <Switch 
                      checked={notifications.sms}
                      onCheckedChange={(checked) => setNotifications({...notifications, sms: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>الإشعارات المباشرة</Label>
                      <p className="text-sm text-gray-500">تلقي إشعارات مباشرة في المتصفح</p>
                    </div>
                    <Switch 
                      checked={notifications.push}
                      onCheckedChange={(checked) => setNotifications({...notifications, push: checked})}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>إنذار نفاد المخزون</Label>
                      <p className="text-sm text-gray-500">تنبيه عند انخفاض المخزون</p>
                    </div>
                    <Switch 
                      checked={notifications.lowStock}
                      onCheckedChange={(checked) => setNotifications({...notifications, lowStock: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>الطلبات الجديدة</Label>
                      <p className="text-sm text-gray-500">تنبيه عند وصول طلبات جديدة</p>
                    </div>
                    <Switch 
                      checked={notifications.newOrders}
                      onCheckedChange={(checked) => setNotifications({...notifications, newOrders: checked})}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>المدفوعات</Label>
                      <p className="text-sm text-gray-500">تنبيه عند استلام المدفوعات</p>
                    </div>
                    <Switch 
                      checked={notifications.payments}
                      onCheckedChange={(checked) => setNotifications({...notifications, payments: checked})}
                    />
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button className="gap-2">
                    <Save className="w-4 h-4" />
                    حفظ الإعدادات
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  إعدادات الأمان
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">كلمة المرور الحالية</Label>
                    <Input id="currentPassword" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">كلمة المرور الجديدة</Label>
                    <Input id="newPassword" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                    <Input id="confirmPassword" type="password" />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>المصادقة الثنائية</Label>
                      <p className="text-sm text-gray-500">زيادة أمان الحساب بالمصادقة الثنائية</p>
                    </div>
                    <Button variant="outline">تفعيل</Button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label>الجلسات النشطة</Label>
                      <p className="text-sm text-gray-500">إدارة الأجهزة المتصلة بحسابك</p>
                    </div>
                    <Button variant="outline">عرض الجلسات</Button>
                  </div>
                </div>
                
                <div className="flex justify-end gap-3">
                  <Button variant="outline" className="gap-2">
                    <Key className="w-4 h-4" />
                    إنشاء نسخة احتياطية
                  </Button>
                  <Button className="gap-2">
                    <Save className="w-4 h-4" />
                    تغيير كلمة المرور
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appearance */}
          <TabsContent value="appearance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  إعدادات المظهر
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label>سمة التطبيق</Label>
                    <p className="text-sm text-gray-500 mb-3">اختر سمة التطبيق المفضلة</p>
                    <div className="grid grid-cols-3 gap-3">
                      <div className="border rounded-lg p-3 cursor-pointer hover:border-primary">
                        <div className="w-full h-16 bg-white border rounded mb-2"></div>
                        <p className="text-sm text-center">فاتح</p>
                      </div>
                      <div className="border rounded-lg p-3 cursor-pointer hover:border-primary">
                        <div className="w-full h-16 bg-gray-900 rounded mb-2"></div>
                        <p className="text-sm text-center">داكن</p>
                      </div>
                      <div className="border rounded-lg p-3 cursor-pointer hover:border-primary border-primary">
                        <div className="w-full h-16 bg-gradient-to-r from-white to-gray-900 rounded mb-2"></div>
                        <p className="text-sm text-center">تلقائي</p>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <Label>اللون الأساسي</Label>
                    <p className="text-sm text-gray-500 mb-3">اختر اللون الأساسي للتطبيق</p>
                    <div className="flex gap-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full cursor-pointer border-2 border-blue-500"></div>
                      <div className="w-8 h-8 bg-green-500 rounded-full cursor-pointer hover:border-2 hover:border-green-500"></div>
                      <div className="w-8 h-8 bg-purple-500 rounded-full cursor-pointer hover:border-2 hover:border-purple-500"></div>
                      <div className="w-8 h-8 bg-red-500 rounded-full cursor-pointer hover:border-2 hover:border-red-500"></div>
                      <div className="w-8 h-8 bg-orange-500 rounded-full cursor-pointer hover:border-2 hover:border-orange-500"></div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button className="gap-2">
                    <Save className="w-4 h-4" />
                    حفظ الإعدادات
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System */}
          <TabsContent value="system" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  إعدادات النظام
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h3 className="font-semibold">النسخ الاحتياطي</h3>
                    <p className="text-sm text-gray-500">إنشاء وإدارة النسخ الاحتياطية</p>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full gap-2">
                        <Download className="w-4 h-4" />
                        تحميل نسخة احتياطية
                      </Button>
                      <Button variant="outline" className="w-full gap-2">
                        <Upload className="w-4 h-4" />
                        استعادة نسخة احتياطية
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h3 className="font-semibold">إدارة البيانات</h3>
                    <p className="text-sm text-gray-500">تنظيف وإدارة بيانات النظام</p>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full gap-2">
                        <Database className="w-4 h-4" />
                        تحسين قاعدة البيانات
                      </Button>
                      <Button variant="destructive" className="w-full gap-2">
                        <Trash2 className="w-4 h-4" />
                        حذف البيانات المؤقتة
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="font-semibold">معلومات النظام</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">إصدار النظام:</span>
                      <span>v2.1.0</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">آخر تحديث:</span>
                      <span>2024-01-15</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">حجم قاعدة البيانات:</span>
                      <span>245 MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">عدد المنتجات:</span>
                      <span>1,234</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Settings;
