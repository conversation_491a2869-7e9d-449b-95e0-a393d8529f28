// Invoice types for the DZ Store Manager application

// Common enums for invoices
export enum InvoiceType {
  SALES = 'sales',
  PURCHASE = 'purchase',
  SALES_RETURN = 'sales_return',
  PURCHASE_RETURN = 'purchase_return'
}

export enum PaymentMethod {
  CASH = 'cash',
  BANK_CARD = 'bank_card',
  CREDIT = 'credit',
  MIXED = 'mixed'
}

// Status enum for invoice state
export enum InvoiceStatus {
  DRAFT = 'draft',
  COMPLETED = 'completed',
  CANCELED = 'canceled',
  RETURNED = 'returned'
}

// مصدر معاد لتأكيد التصدير
export { InvoiceStatus };

export enum PaymentStatus {
  PAID = 'paid',
  PARTIAL = 'partial',
  UNPAID = 'unpaid'
}

// Invoice item interface
export interface InvoiceItem {
  id?: string;
  product_id: string;
  name: string;
  barcode?: string;
  quantity: number;
  unit_price: number;
  discount_amount?: number;
  discount_percent?: number;
  tax_percent?: number;
  tax_amount?: number;
  total_price: number;
  notes?: string;
}

// Invoice form data interface
export interface InvoiceFormData {
  id?: string;
  invoice_number: string;
  reference_number?: string;
  customer_id?: string;
  supplier_id?: string;
  warehouse_id?: string;
  type: InvoiceType;
  status: InvoiceStatus;
  invoice_date: string;
  due_date?: string;
  notes?: string;
  subtotal: number;
  discount_amount: number;
  discount_percent: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  payment_status: PaymentStatus;
  payment_method: PaymentMethod;
  items: Omit<InvoiceItem, 'id'>[];
  return_reason?: string;
  returned_from_invoice_id?: string;
}

// Sales invoice interface (first definition)
export interface SalesInvoice extends BaseInvoice {
  customer_id: string;
  customer?: {
    name: string;
    phone: string;
  };
}

// Purchase invoice interface (first definition - to be overwritten)
export interface PurchaseInvoice extends BaseInvoice {
  supplier_id: string;
  supplier?: {
    name: string;
    phone: string;
  };
}

// Base invoice interface
export interface BaseInvoice {
  id: string;
  invoice_number: string;
  reference_number?: string;
  created_at?: string;
  updated_at?: string;
  invoice_date?: string;
  due_date?: string;
  notes?: string;
  subtotal: number;
  discount_percent?: number;
  discount_amount?: number;
  tax_rate?: number; // تغيير من tax_percent لمطابقة قاعدة البيانات
  tax_amount?: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  status: InvoiceStatus;
  payment_status: PaymentStatus;
  payment_method: PaymentMethod;
  items?: InvoiceItem[];
  type: InvoiceType;
  warehouse_id?: string;
}

// Sales invoice interface (overwriting the first definition)
export interface SalesInvoice extends BaseInvoice {
  type: InvoiceType.SALES | InvoiceType.SALES_RETURN;
  customer_id: string;
  customer_name?: string;
  returned_from_invoice_id?: string;
  return_reason?: string;
}

// Purchase invoice interface
export interface PurchaseInvoice extends BaseInvoice {
  type: InvoiceType.PURCHASE | InvoiceType.PURCHASE_RETURN;
  supplier_id: string;
  supplier_name?: string;
  returned_from_invoice_id?: string;
  return_reason?: string;
  warehouse_id?: string;
}

// Payment transaction interface
export interface PaymentTransaction {
  id: string;
  invoice_id: string;
  amount: number;
  payment_method: PaymentMethod;
  payment_date: string;
  notes?: string;
}

// Type guard functions
export const isSalesInvoice = (invoice: SalesInvoice | PurchaseInvoice | BaseInvoice): invoice is SalesInvoice => {
  return invoice.type === InvoiceType.SALES || invoice.type === InvoiceType.SALES_RETURN;
};

export const isPurchaseInvoice = (invoice: SalesInvoice | PurchaseInvoice | BaseInvoice): invoice is PurchaseInvoice => {
  return invoice.type === InvoiceType.PURCHASE || invoice.type === InvoiceType.PURCHASE_RETURN;
};

