-- Add the discount_percent column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'discount_percent'
  ) THEN
    ALTER TABLE invoices ADD COLUMN discount_percent DECIMAL(5,2) DEFAULT 0;
  END IF;
END $$;

-- Make sure the tax_rate column exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'tax_rate'
  ) THEN
    ALTER TABLE invoices ADD COLUMN tax_rate DECIMAL(5,2) DEFAULT 0;
  END IF;
END $$;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
