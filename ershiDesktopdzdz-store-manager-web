[33mcommit 3c70c57d2071d627c0c258768f0acdefa2be45c9[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m, [m[1;31morigin/HEAD[m[33m)[m
Author: gpt-engineer-app[bot] <159125892+gpt-engineer-app[bot]@users.noreply.github.com>
Date:   Mon Jun 16 19:51:25 2025 +0000

    Fix:
    
    Address ERR_BLOCKED_BY_CLIENT errors.

[1mdiff --git a/src/components/AddInvoiceDialog.tsx b/src/components/AddInvoiceDialog.tsx[m
[1mindex 40094f8..2136d77 100644[m
[1m--- a/src/components/AddInvoiceDialog.tsx[m
[1m+++ b/src/components/AddInvoiceDialog.tsx[m
[36m@@ -125,15 +125,17 @@[m [mexport function AddInvoiceDialog({ open, onOpenChange, onInvoiceAdded }: AddInvo[m
     }[m
 [m
     try {[m
[31m-      // إعداد بيانات الفاتورة مع معالجة تاريخ الاستحقاق[m
[32m+[m[32m      // إعداد بيانات الفاتورة مع معالجة تاريخ الاستحقاق واستخدام القيم الصحيحة للـ enum[m
       const invoiceData = {[m
         ...formData,[m
[31m-        due_date: formData.due_date || null, // تحويل السلسلة الفارغة إلى null[m
[32m+[m[32m        due_date: formData.due_date || null,[m
         total_amount: getTotalAmount(),[m
         remaining_amount: getTotalAmount(),[m
[31m-        payment_status: 'unpaid'[m
[32m+[m[32m        payment_status: 'unpaid' as const // استخدام قيمة صحيحة من enum[m
       };[m
 [m
[32m+[m[32m      console.log('Invoice data to be sent:', invoiceData);[m
[32m+[m
       // إضافة الفاتورة[m
       const { data: invoice, error: invoiceError } = await supabase[m
         .from('invoices')[m
[36m@@ -141,7 +143,10 @@[m [mexport function AddInvoiceDialog({ open, onOpenChange, onInvoiceAdded }: AddInvo[m
         .select()[m
         .single();[m
 [m
[31m-      if (invoiceError) throw invoiceError;[m
[32m+[m[32m      if (invoiceError) {[m
[32m+[m[32m        console.error('Invoice error:', invoiceError);[m
[32m+[m[32m        throw invoiceError;[m
[32m+[m[32m      }[m
 [m
       // إضافة عناصر الفاتورة[m
       const invoiceItems = items.map(item => ({[m
